用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【办事方式Tab】Tab展示和切换功能验证,P0,"已登录移动端系统;已进入政策详情页面","1. 观察办事方式tab栏的显示顺序，检查六种办理方式的排列
2. 观察常用办理方式是否显示[常用]标签
3. 点击不同的办理方式tab，观察页面内容切换效果
4. 检查当前政策已添加的办理方式tab显示情况","1. 办理方式tab按车管所办理、云车服办理、邮局办理、警邮平台办理、服务站办理、政务中心办理顺序展示
2. 常用办理方式tab显示[常用]标签标识
3. 点击不同tab时页面内容正确切换至对应办理方式的政策
4. 只展示当前政策已添加的办理方式tab"
TC002,【特殊情况】字段显示和联动验证,P0,"已登录移动端系统;已进入政策详情页面","1. 观察[特殊情况]字段的显示内容
2. 观察[当前机构是否可以办理线上抵押]字段选择[是]时的页面变化
3. 观察[当前机构是否可以办理线上抵押]字段选择[否]时的页面变化
4. 检查特殊情况字段为空时的显示内容","1. 特殊情况字段正确显示内容
2. 选择[是]时联动展示其他相关字段
3. 选择[否]时不展示其他字段
4. 特殊情况为空时显示「待调研」"
TC003,【资料明细】私户车和公户车资料显示验证,P0,"已登录移动端系统;已进入政策详情页面;当前机构可以办理线上抵押","1. 观察私户车线上抵押资料列表的显示内容，检查序号、资料名称、盖章类型、数量、资料模版、是否为车管所特殊模版字段
2. 检查机动车登记证书、代理人身份证原件是否显示为默认资料
3. 观察公户车线上抵押资料列表的显示内容
4. 检查新增的三个资料（代理人身份证原件、抵押人委托书、情况说明）的显示顺序","1. 私户车资料列表正确显示所有字段信息，数据完整
2. 机动车登记证书、代理人身份证原件显示为默认资料，无删除操作
3. 公户车资料列表显示内容与私户车相同
4. 新增三个资料的优先级仅高于自定义资料，排序正确"
TC004,【资料明细】复制资料和下载模版功能验证,P1,"已登录移动端系统;已进入政策详情页面;资料列表中包含自定义资料","1. 点击[复制资料]按钮，检查复制到剪贴板的内容
2. 将复制的内容粘贴到文本编辑器中，观察资料顺序和内容格式
3. 检查盖章类型为无需盖章的资料复制内容
4. 点击[下载模板]按钮，观察下载的压缩包文件
5. 点击已上传资料模版的资料名称，观察页面跳转","1. 成功复制资料名称、盖章类型、份数三列内容到剪贴板
2. 粘贴内容的资料顺序正确，格式规范
3. 无需盖章的资料只复制资料名称和份数
4. 下载的压缩包文件名为线上抵押，包含所有模板文件
5. 点击资料模版名称跳转到新页面查看模板"
TC005,【资料明细】空值处理验证,P2,"已登录移动端系统;已进入政策详情页面;存在字段值为空的资料","1. 观察盖章类型字段为空的资料显示内容
2. 观察资料模版字段为空的资料显示内容
3. 观察是否为车管所特殊模版字段为空的资料显示内容
4. 观察资料特殊要求说明字段为空时的显示内容","1. 盖章类型为空时显示「待调研」
2. 资料模版为空时显示「待调研」
3. 是否为车管所特殊模版为空时显示「待调研」
4. 资料特殊要求说明为空时显示「待调研」"
TC006,【抵押要求】抵押人到场联动和基础字段验证,P0,"已登录移动端系统;已进入政策详情页面","1. 观察抵押人本人到场资料已添加时[抵押人是否需要到场]字段的状态
2. 观察抵押人本人到场资料未添加时[抵押人是否需要到场]字段的状态
3. 检查[车辆抵押状态下是否可以办理营运证]、[合同上是否可以盖合同专用章]、[营运车辆是否可以抵押]字段的选项
4. 观察[脱审对抵押有无影响]、[抵押资料填写要求]、[有违章对抵押有无影响]、[黄牌车/货车特殊情况备注]字段的默认内容","1. 抵押人本人到场资料已添加时，抵押人是否需要到场=是且不可修改
2. 抵押人本人到场资料未添加时，抵押人是否需要到场=否
3. 三个单选字段均显示是/否选项，可正常选择
4. 四个输入框字段为空时显示「待调研」"
TC007,【收费情况】费用显示和空值处理验证,P0,"已登录移动端系统;已进入政策详情页面","1. 观察[车管所办理抵押的工本费]字段的费用和费用备注显示
2. 观察各渠道费用字段（抵押人不到场渠道费、柜台非官方收费、买号费等）的显示内容
3. 观察[加急归档费用]字段的显示内容
4. 检查[归档期是否可以加急]字段的显示格式
5. 验证费用为空和费用备注为空时的显示效果","1. 车管所办理抵押的工本费正确显示费用和费用备注信息
2. 各渠道费用字段正确显示对应的费用和备注信息
3. 加急归档费用字段正确显示费用和备注信息
4. 归档期是否可以加急显示是/否格式
5. 费用为空时显示「--」，费用备注为空时显示「待调研」，加急归档费用为空时显示「0」"
TC008,【抵押时效】办理时机和预约相关字段验证,P1,"已登录移动端系统;已进入政策详情页面","1. 观察办理时机相关字段的显示内容和选项
2. 观察预约相关字段的显示内容
3. 观察出证时效相关字段的显示内容
4. 检查各字段为空时的显示效果","1. 新车上牌和抵押是否可以一起办理、二手车过户和抵押是否可以一起办理、新车上牌后多久可抵押显示是/否选项
2. 抵押是否需要预约显示是/否选项，其他预约字段显示对应内容
3. 出证时效相关字段显示对应的时间和类型信息
4. 字段为空时显示「待调研」"
TC009,【办理场所要求】场所相关字段验证,P1,"已登录移动端系统;已进入政策详情页面","1. 观察[户籍为县城的抵押权人是否可到市区上牌和抵押]字段的显示内容
2. 观察[办理地点具体名称]字段的显示内容
3. 观察[县城上牌的车辆是否可在市区抵押]字段的显示内容
4. 观察[办理机构是否可现场收费]字段的显示格式
5. 检查各字段为空时的显示效果","1. 户籍为县城的抵押权人是否可到市区上牌和抵押字段正确显示内容
2. 办理地点具体名称字段正确显示内容
3. 县城上牌的车辆是否可在市区抵押字段正确显示内容
4. 办理机构是否可现场收费显示是/否格式
5. 字段为空时显示「待调研」"
TC010,【批量抵押】批量抵押字段验证,P1,"已登录移动端系统;已进入政策详情页面","1. 观察[是否可批量抵押]字段的显示格式
2. 观察[一人一天最大办理次数]字段的显示内容
3. 观察[一人一次最多办理抵押单的数量]字段的显示内容
4. 观察[批量车当天可以出本的上限台数]字段的显示内容
5. 检查各字段为空时的显示效果","1. 是否可批量抵押显示是/否格式
2. 一人一天最大办理次数字段正确显示内容
3. 一人一次最多办理抵押单的数量字段正确显示内容
4. 批量车当天可以出本的上限台数字段正确显示内容
5. 字段为空时显示「待调研」"
TC011,【移动端业务政策列表】筛选和列表显示验证,P0,"已登录移动端系统;已进入业务政策列表页面","1. 在筛选项[业务类型]下拉框中观察是否包含线上抵押选项
2. 选择线上抵押业务类型，点击搜索按钮，观察搜索结果
3. 观察列表中线上抵押政策的字段显示内容
4. 检查数据权限控制效果","1. 筛选项业务类型下拉框包含线上抵押选项
2. 搜索线上抵押成功，返回相关政策列表
3. 列表显示【线上抵押】政策，主体类型展示对应的值
4. 只能看到有查看权限的地区下的线上抵押政策"
TC012,【移动端业务政策列表】修改权限验证,P1,"已登录移动端系统;已进入业务政策列表页面;存在线上抵押政策","1. 观察有修改权限且无待确认和待审核月度核实单的政策[修改]按钮显示
2. 观察无修改权限的政策[修改]按钮显示
3. 观察有待确认或待审核月度核实单的政策[修改]按钮显示
4. 观察无业务维护权限的政策[修改]按钮显示","1. 满足所有修改条件的政策显示[修改]按钮
2. 无修改权限的政策不显示[修改]按钮
3. 有待确认或待审核月度核实单的政策不显示[修改]按钮
4. 无业务维护权限的政策不显示[修改]按钮"