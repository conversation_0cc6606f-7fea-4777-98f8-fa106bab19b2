用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【弹窗验证】政策类型和主体类型选择验证,P0,已登录系统,"1. 解除车辆抵押后点击新增政策，在政策类型下拉框中选择[线上抵押]，观察页面变化
2. 在主体类型下拉框中依次选择[融资租赁公司]、[汽车金融公司]、[银行]等选项，观察选择结果","1. 政策类型成功选择线上抵押，页面显示对应的配置选项
2. 主体类型下拉框包含融资租赁公司、汽车金融公司、银行、金融租赁公司、保险公司、担保公司、财务公司7个选项，选择正常"
TC002,【弹窗验证】省份城市和区域级联选择验证,P0,已登录系统,"1. 在省份下拉框中选择[北京市]，观察城市下拉框变化
2. 在城市下拉框中选择具体城市，观察区域选择范围
3. 在区域字段中观察可选择的区域范围","1. 选择省份后城市下拉框显示对应省份的城市列表
2. 选择城市后区域字段显示该城市的所有区域
3. 区域显示城市全部区域，可正常选择"
TC003,【弹窗验证】办理方式选择和常用办理方式验证,P1,已登录系统,"1. 在办理方式下拉框中观察可选项内容
2. 在常用办理方式字段中从所有办理方式中选择其一，观察选择范围","1. 办理方式下拉框包含车管所办理、云车服办理、邮局办理、警邮平台办理、服务站办理、政务中心办理6个选项
2. 常用办理方式可从所有办理方式中选择其一，选择范围正确"
TC004,【特殊情况】机构办理能力配置验证,P0,已登录系统且完成弹窗验证,"1. 在特殊情况文本框中输入特殊说明信息，观察输入效果
2. 在[当前机构是否可以办理线上抵押]字段选择[是]，观察页面字段显示变化
3. 在[当前机构是否可以办理线上抵押]字段选择[否]，观察页面字段隐藏情况","1. 特殊情况文本框正常输入文本信息
2. 选择[是]后页面展示其他所有配置字段
3. 选择[否]后页面隐藏其他配置字段，仅显示特殊情况说明"
TC005,【资料明细】私户车资料选择和配置验证,P0,"已登录系统;当前机构可以办理线上抵押=是","1. 在私户车线上抵押资料选择中添加[代理人身份证原件]，观察资料列表变化
2. 添加[抵押人委托书]和[情况说明]资料，观察新增资料显示
3. 添加[抵押人本人到场]资料，观察联动字段变化
4. 在资料特殊要求说明输入框中输入特殊要求，观察输入效果
5. 观察默认资料的删除按钮显示情况","1. 代理人身份证原件成功添加到资料列表，显示序号、资料名称等字段
2. 抵押人委托书和情况说明成功添加，资料列表正确显示
3. 添加抵押人本人到场资料后，抵押人是否需要到场字段联动变为[是]且不可修改，抵押人不到场渠道费字段变为可填写状态
4. 资料特殊要求说明正常输入文本内容
5. 机动车登记证书、代理人身份证原件为默认资料，不显示删除按钮"
TC006,【资料明细】公户车资料配置验证,P1,"已登录系统;当前机构可以办理线上抵押=是","1. 切换到公户车线上抵押资料配置，观察资料选择选项
2. 添加公户车相关资料，观察资料列表显示
3. 配置公户车资料的盖章类型、数量等信息，观察配置效果","1. 公户车资料选择选项与私户车相同，包含代理人身份证原件、抵押人委托书等
2. 公户车资料成功添加到列表，显示完整的资料信息
3. 盖章类型、数量、资料模版等字段正常配置"
TC007,【抵押要求】抵押人到场和联动规则验证,P0,"已登录系统;已配置资料明细","1. 在未添加[抵押人本人到场]资料的情况下，观察[抵押人是否需要到场]字段状态
2. 添加[抵押人本人到场]资料后，观察[抵押人是否需要到场]字段变化
3. 观察[脱审对抵押有无影响]输入框的默认内容
4. 观察[抵押资料填写要求]输入框的默认内容和蓝色提示文字
5. 观察[黄牌车/货车特殊情况备注]输入框的默认内容和蓝色提示文字
6. 观察[有违章对抵押有无影响]输入框的默认内容","1. 未添加抵押人本人到场资料时，抵押人是否需要到场=否，不可修改
2. 添加抵押人本人到场资料后，抵押人是否需要到场=是，不可修改
3. 脱审对抵押有无影响输入框默认显示""脱审对抵押有无影响""
4. 抵押资料填写要求输入框默认显示""抵押资料填写要求""，上方显示蓝色提示文字""如必须机打、抬头等特殊填写要求""
5. 黄牌车/货车特殊情况备注输入框默认显示""黄牌车/货车特殊情况备注""，上方显示蓝色提示文字""若黄牌车/货车等大车政策跟小车政策不同请在此备注""
6. 有违章对抵押有无影响输入框默认显示""有违章对抵押有无影响"""
TC008,【抵押要求】单选项验证,P1,"已登录系统;已配置基本信息","1. 在[车辆抵押状态下是否可以办理营运证]字段选择[是]和[否]，观察选择效果
2. 在[合同上是否可以盖合同专用章]字段选择[是]和[否]，观察选择效果
3. 在[营运车辆是否可以抵押]字段选择[是]和[否]，观察选择效果","1. 车辆抵押状态下是否可以办理营运证正常选择是/否选项
2. 合同上是否可以盖合同专用章正常选择是/否选项
3. 营运车辆是否可以抵押正常选择是/否选项"
TC009,【收费情况】基础收费项目配置验证,P0,"已登录系统;已配置抵押要求","1. 在[车管所办理抵押的工本费]字段输入数字100和费用备注，观察输入验证
2. 在[柜台非官方收费]字段输入数字50和费用备注，观察蓝色提示文字
3. 在[买号费]字段输入数字30和费用备注，观察蓝色提示文字显示","1. 车管所办理抵押的工本费支持输入纯数字和费用备注，格式验证正确
2. 柜台非官方收费正常输入，上方显示蓝色提示""私下付给工作人员的费用""
3. 买号费正常输入，上方显示蓝色提示""线上抵押，线下打证需要约号。找渠道买号可以不用代理人或车主约号的费用"""
TC010,【收费情况】联动收费项目验证,P1,"已登录系统;已添加抵押人本人到场资料","1. 观察[抵押人不到场渠道费]字段的可编辑状态
2. 删除[抵押人本人到场]资料，观察[抵押人不到场渠道费]字段变化
3. 在[归档期是否可以加急]字段选择相关选项，观察[加急归档费用]字段显示","1. 已添加抵押人本人到场资料时，抵押人不到场渠道费字段可填写
2. 删除抵押人本人到场资料后，抵押人不到场渠道费显示""无需到场""且不可修改
3. 归档期是否可以加急=是时展示加急归档费用字段，=否时隐藏该字段"
TC011,【收费情况】特殊收费项目配置验证,P1,"已登录系统;已配置基础收费","1. 在[车管所办理抵押的渠道费]字段输入费用信息，观察蓝色提示文字
2. 在[牛工代办费]字段输入费用信息，观察蓝色提示文字
3. 在[提档费]字段输入费用信息，观察蓝色提示文字显示
4. 在[加急归档费用]字段输入费用信息，观察蓝色提示文字显示","1. 车管所办理抵押的渠道费上方显示蓝色提示""线上抵押推送成功后，不能由推送的代理人打证，必须找渠道打证的费用""
2. 牛工代办费上方显示蓝色提示""线上抵押推送的代理人是牛工，牛工线下去打证。牛工每单的计件费用""
3. 提档费上方显示蓝色提示""可以把县城档案提到市区打证的提档费用。线上抵押市区无法通办，必须到对应的车管所打证""
4. 加急归档费用上方显示蓝色提示""找渠道加急归档的费用。车辆解押后未归档导致线上抵押推送失败"""
TC012,【抵押时效】基础时效配置验证,P0,"已登录系统;已配置收费情况","1. 在[新车上牌和抵押是否可以一起办理]字段选择[是]和[否]，观察选择效果
2. 在[二手车过户和抵押是否可以一起办理]字段选择[是]和[否]，观察选择效果
3. 观察[收到资料齐全后多久可以出证]输入框的默认内容
4. 观察[新车上牌后多久可抵押]字段类型
5. 观察[二手车过户后多久可抵押]输入框的默认内容
6. 观察[最长出证时间]输入框的默认内容","1. 新车上牌和抵押是否可以一起办理正常选择是/否选项
2. 二手车过户和抵押是否可以一起办理正常选择是/否选项
3. 收到资料齐全后多久可以出证输入框默认显示""收到材料齐全后多久可以出证""
4. 新车上牌后多久可抵押为单选项，选项为是/否
5. 二手车过户后多久可抵押输入框默认显示""二手车过户后多久可以办理抵押""
6. 最长出证时间输入框默认显示""最长出证时间"""
TC013,【抵押时效】预约相关联动验证,P1,"已登录系统;已配置基础时效","1. 在[抵押是否需要预约]字段选择[是]，观察相关字段显示变化和默认内容
2. 在[抵押需提前多久预约]和[约号流程说明]字段输入信息，观察默认内容
3. 在[约号方式]下拉框中选择枚举值，观察选项内容
4. 将[抵押是否需要预约]改为[否]，观察字段隐藏情况","1. 选择抵押是否需要预约=是后，展示抵押需提前多久预约（默认显示""抵押需提前多久预约""）、约号流程说明（默认显示""约号流程说明""）、约号方式字段
2. 抵押需提前多久预约和约号流程说明字段正常输入，显示对应默认内容
3. 约号方式下拉框包含现场取号机取号、【佛山交管】公众号、【惠警办】小程序、【i莞家】公众号、【广州交警】公众号、【善美村居】小程序、【苏州交警】公众号、【上海交警】APP共8个枚举值
4. 选择抵押是否需要预约=否后，隐藏抵押需提前多久预约、约号流程说明、约号方式字段"
TC014,【抵押时效】出证时间和回执联动验证,P1,"已登录系统;已配置预约信息","1. 在[最长出证时间]输入框中输入数字1，观察[办理回执类型]和[凭证样式]字段显示
2. 在[最长出证时间]输入框中输入数字2，观察[办理回执类型]和[凭证样式]字段变化
3. 在[办理回执类型]下拉框中选择枚举值，观察选项内容
4. 在[凭证样式]字段上传图片，观察上传功能","1. 最长出证时间=1时，不展示办理回执类型和凭证样式字段
2. 最长出证时间>1时，展示办理回执类型和凭证样式字段且为必填
3. 办理回执类型下拉框包含警邮系统完成凭证、申请表照片、业务授理凭证、应收材料清单、云车服系统截图5个选项
4. 凭证样式支持上传图片功能，上传正常"
TC015,【办理场所要求】场所信息配置验证,P1,"已登录系统;已配置抵押时效","1. 观察[户籍为县城的抵押权人是否可到市区上牌和抵押]输入框的默认内容
2. 观察[办理地点具体名称]输入框的默认内容
3. 观察[县城上牌的车辆是否可在市区抵押]输入框的默认内容
4. 在[办理机构是否可现场收费]字段选择[是]和[否]，观察选择效果","1. 户籍为县城的抵押权人是否可到市区上牌和抵押输入框默认显示""户籍为县城的抵押权人是否可到市区上牌和抵押""
2. 办理地点具体名称输入框默认显示""当前办理抵押业务地点名称""
3. 县城上牌的车辆是否可在市区抵押输入框默认显示""县城上牌的车辆是否可在市区抵押""
4. 办理机构是否可现场收费正常选择是/否选项"
TC016,【批量抵押】批量处理配置验证,P1,"已登录系统;已配置办理场所要求","1. 在[是否可批量抵押]字段选择[是]和[否]，观察选择效果
2. 观察[一人一天最大办理次数]输入框的默认内容
3. 观察[一人一次最多办理抵押单的数量]输入框的默认内容
4. 观察[批量车当天可以出本的上限台数]输入框的默认内容和蓝色提示","1. 是否可批量抵押正常选择是/否选项
2. 一人一天最大办理次数输入框默认显示""一人一天最多能办理多少次""
3. 一人一次最多办理抵押单的数量输入框默认显示""一人一次最多能办理多少台车的抵押""
4. 批量车当天可以出本的上限台数输入框默认显示""批量车当天可以出本的上限台数""，上方显示蓝色提示""一天最多可以办理多少本"""
TC017,【数据验证】必填项和格式验证,P0,"已登录系统;已配置所有功能模块","1. 清空所有必填项内容，点击保存按钮，观察验证提示
2. 在收费相关字段输入非数字内容，观察格式验证
3. 在最长出证时间字段输入非数字内容，观察格式验证
4. 完整填写所有必填项，点击保存按钮，观察保存结果","1. 必填项为空时显示相应的验证错误提示信息
2. 收费字段输入非数字内容时提示格式错误
3. 最长出证时间输入非数字内容时提示格式错误
4. 所有必填项填写完整且格式正确时保存成功"
TC018,【功能集成】完整流程验证,P0,已登录系统,"1. 从弹窗验证开始，依次完成政策类型选择、主体类型选择、省份城市选择
2. 配置特殊情况为机构可办理线上抵押
3. 完整配置私户车和公户车资料明细
4. 配置所有抵押要求字段
5. 配置所有收费情况字段
6. 配置所有抵押时效字段
7. 配置办理场所要求和批量抵押
8. 保存完整的线上抵押政策配置","1. 弹窗验证各字段选择正常，级联关系正确
2. 特殊情况配置正确，字段联动显示正常
3. 资料明细配置完整，联动规则生效
4. 抵押要求各字段配置正确，默认内容显示正常
5. 收费情况各字段配置正确，联动规则和提示信息显示正常
6. 抵押时效各字段配置正确，联动规则生效
7. 办理场所和批量抵押配置正确
8. 完整的线上抵押政策保存成功，所有配置信息正确存储"