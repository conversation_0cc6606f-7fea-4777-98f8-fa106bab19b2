用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【页面结构】办理方式tab和搜索功能验证,P0,"已登录系统;已进入变更日志页面","1. 观察页面中办理方式tab的显示内容，检查六种办理方式的展示
2. 观察常用办理方式tab是否显示[常用]标签
3. 在搜索框中观察默认提示内容
4. 在搜索框中输入带有中括号的字段名称，点击搜索按钮
5. 在搜索框中输入不带中括号的字段内容，点击搜索按钮","1. 页面正确显示六种办理方式的tab选项
2. 常用办理方式tab显示[常用]标签标识
3. 搜索框显示默认内容「请输入字段名称」
4. 带中括号的字段名称搜索成功，返回匹配结果
5. 不带中括号的字段内容搜索失败，无匹配结果"
TC002,【日志内容】基本信息显示验证,P0,"已登录系统;已进入变更日志页面;存在变更记录","1. 观察变更日志记录的更新时间格式显示
2. 观察修改人信息的显示格式
3. 观察变更详情内容的展示情况","1. 更新时间按xxxx-xx-xx xx：xx格式正确显示
2. 修改人按角色名称-账号名称格式正确显示
3. 变更详情内容完整展示具体的变更信息"
TC003,【记录节点】PC端和小程序端修改记录验证,P0,"已登录系统;存在PC端修改和小程序端修改的记录","1. 观察PC端修改记录的时间显示
2. 观察小程序端主动修改且PC全部审核通过的记录时间
3. 观察小程序端主动修改且PC部分字段审核不通过的记录显示
4. 观察小程序二次修改且字段与最初不同的记录显示
5. 观察小程序二次修改且字段与最初相同的记录显示","1. PC端修改记录显示修改的具体时间
2. 小程序端全部审核通过记录显示审核通过的时间
3. 部分审核不通过时，通过的字段展示日志，不通过的字段不展示
4. 二次修改字段与最初不同时记录日志和审核时间
5. 二次修改字段与最初相同时不记录日志"
TC004,【记录节点】小程序核实功能记录验证,P1,"已登录系统;存在小程序核实相关的记录","1. 观察小程序核实记录的时间显示
2. 观察小程序核实且PC审核部分字段不通过的记录显示
3. 观察小程序核实二次核实且字段与最初不同的记录显示
4. 观察小程序核实二次修改且字段与最初相同的记录显示","1. 小程序核实记录显示核实修改的具体时间
2. 部分审核不通过时，通过的字段展示日志，不通过的字段不展示
3. 二次核实字段与最初不同时记录日志和审核时间
4. 二次修改字段与最初相同时不记录日志"
TC005,【变更类型】常用办理方式和使用地区变更验证,P0,"已登录系统;存在常用办理方式和使用地区变更记录","1. 观察车管所办理tab中常用办理方式变更的日志内容格式
2. 观察云车服办理tab中常用办理方式变更的日志内容格式
3. 观察地区数据从无到有的变更记录格式
4. 观察地区数据从有到无的变更记录格式
5. 观察地区数据从有到有的变更记录格式","1. 车管所办理tab显示【常用办理方式】车管所办理→云车服办理格式
2. 云车服办理tab显示【常用办理方式】车管所办理→云车服办理格式
3. 地区从无到有显示--→省份-城市-区县1、区县2···格式
4. 地区从有到无显示省份-城市-区县1、区县2···→--格式
5. 地区从有到有显示省份-城市-区县1、区县2···→省份-城市-区县1、区县2···格式"
TC006,【变更类型】非资料明细字段变更验证,P1,"已登录系统;存在特殊情况、抵押要求、收费情况等字段变更记录","1. 观察修改前字段非空的变更记录显示格式
2. 观察修改前字段为空的变更记录显示格式
3. 检查特殊情况字段变更的记录格式
4. 检查抵押要求字段变更的记录格式
5. 检查收费情况、抵押时效、办理场所要求、批量抵押字段变更的记录格式","1. 修改前字段非空时显示字段名称：**→**格式
2. 修改前字段为空时显示字段名称：字段内容格式
3. 特殊情况字段变更按非资料明细字段格式正确显示
4. 抵押要求字段变更按非资料明细字段格式正确显示
5. 其他模块字段变更均按非资料明细字段格式正确显示"
TC007,【变更类型】资料明细字段变更验证,P0,"已登录系统;存在资料明细相关的变更记录","1. 观察新增一份资料的变更记录显示格式
2. 观察删除一份资料的变更记录显示格式
3. 观察修改现有资料内容的变更记录显示格式
4. 观察修改自定义资料信息的变更记录显示格式","1. 新增资料显示【资料模块名称】：新增 资料名称 盖章类型 数量 模板 是否是车管所特殊模板格式
2. 删除资料显示【资料模块名称】：删除 资料名称 盖章类型 数量 模板 是否是车管所特殊模板格式
3. 修改现有资料显示【公司备案】：抵押委托书样本 鲜章→电子章 1份→2份 模板A.pdf→模板B.pdf 是车管所特殊模板→非车管所特殊模板格式
4. 修改自定义资料显示【公司备案】：自定义资料名称 资料A→资料B 鲜章→电子章 1份→2份 模板A.pdf→模板B.pdf 是车管所特殊模板→非车管所特殊模板格式"
TC008,【变更类型】线上抵押办理能力字段变更验证,P1,"已登录系统;存在当前机构是否可以办理线上抵押字段变更记录","1. 观察字段从[是]变为[否]时的日志记录内容
2. 观察字段从[否]变为[是]时的日志记录内容
3. 检查字段从[是]变为[否]时其他子级字段是否在日志中体现
4. 检查字段从[否]变为[是]时其他子级字段是否在日志中体现","1. 字段从[是]变为[否]时日志中只记录该字段的变动
2. 字段从[否]变为[是]时日志中记录该字段和其他子级的变动
3. 字段从[是]变为[否]时其他子级字段不在日志中体现
4. 字段从[否]变为[是]时其他子级字段的变动都在日志中体现"
TC009,【政策信息库】筛选项和列表字段更新验证,P1,"已登录系统;已进入政策信息库页面","1. 观察筛选项[业务类型]中是否包含线上抵押选项
2. 观察列表字段[业务类型]中是否显示线上抵押类型
3. 观察政策信息列表是否包含[添加地区]功能","1. 筛选项[业务类型]成功新增线上抵押选项，可正常选择
2. 列表字段[业务类型]正确显示线上抵押类型数据
3. 政策信息列表成功添加[添加地区]功能按钮"
TC010,【备用金功能】线上抵押业务类型集成验证,P1,"已登录系统;已进入备用金相关页面","1. 在备用金业务类型列表页面观察筛选项[关联政策业务类型]是否包含线上抵押
2. 在备用金业务类型列表中观察[关联政策业务类型]字段是否显示线上抵押
3. 尝试新建备用金业务类型并关联线上抵押，观察新建结果
4. 在费用基础配置页面观察筛选项和列表字段的线上抵押选项
5. 尝试新建费用基础配置并选择关联线上抵押的备用金业务类型","1. 筛选项[关联政策业务类型]成功新增线上抵押选项
2. 列表字段[关联政策业务类型]正确显示线上抵押数据
3. 新建备用金业务类型关联线上抵押操作成功
4. 费用基础配置页面筛选项和列表字段均包含线上抵押选项
5. 新建费用基础配置选择关联线上抵押的业务类型操作成功"
TC011,【备用金价格标准】线上抵押业务类型新建验证,P1,"已登录系统;已进入备用金价格标准页面","1. 点击新建备用金价格标准，选择关联线上抵押的备用金业务类型
2. 选择省份城市和区域，观察区县信息的选择范围限制
3. 选择办理团队和办理方式
4. 添加费用用途、费用原因和费用备注信息
5. 点击保存按钮，观察新建结果","1. 成功选择关联线上抵押的备用金业务类型
2. 区县信息只能选择到当前城市下的政策区县组合，选择范围正确
3. 办理团队和办理方式选择正常
4. 费用用途、费用原因和费用备注信息添加成功
5. 备用金价格标准新建成功，数据保存正确"
TC012,【车牌归属定位】车牌查询功能验证,P1,"已登录系统;已进入政策信息库页面","1. 观察筛选项[地区]后是否显示[车牌查询]按钮
2. 点击[车牌查询]按钮，观察弹窗打开情况
3. 在车牌归属弹窗中输入[鲁B]，观察地区筛选框的自动填充结果
4. 在车牌归属弹窗中输入[京A]，观察地区筛选框的自动填充结果
5. 在车牌归属弹窗中输入[桂A]、[鄂M]、[新C]，观察各自的自动填充结果","1. 筛选项[地区]后成功显示[车牌查询]按钮
2. 点击按钮后[车牌归属]弹窗正常打开
3. 输入[鲁B]后地区筛选框自动填充山东省-青岛市
4. 输入[京A]后地区筛选框自动填充北京市-北京市
5. [桂A]填充广西省-南宁市，[鄂M]填充湖北省-仙桃市，[新C]填充新疆省-石河子市"
TC013,【人员权限】业务权限配置验证,P0,"已登录系统;具有人员设置权限;已进入人员设置页面","1. 观察运营管理员权限配置中是否包含[线上抵押]选项
2. 观察政策运营权限配置中是否包含[线上抵押]选项
3. 观察政策专员权限配置中是否包含[线上抵押]选项
4. 观察政策维护人权限配置中是否包含[线上抵押]选项
5. 观察备用金运营权限配置中是否包含[线上抵押]选项","1. 运营管理员权限配置成功新增[线上抵押]选项
2. 政策运营权限配置无[线上抵押]选项
3. 政策专员权限配置无[线上抵押]选项
4. 政策维护人权限配置成功新增[线上抵押]选项
5. 备用金运营权限配置无[线上抵押]选项"
TC014,【权限影响范围】不同角色权限验证,P0,"已配置不同角色的线上抵押权限","1. 使用运营管理员test0412010账号登录，观察线上抵押政策的操作权限
2. 使用政策运营scrmsjadmin账号登录，观察线上抵押政策的权限限制
3. 使用政策专员test0109账号登录，观察线上抵押政策的权限限制
4. 使用政策维护人wuzheng账号登录，观察线上抵押政策的操作权限
5. 在小程序端使用运营管理员账号观察详情页的修改按钮权限","1. 运营管理员拥有线上抵押政策新建、修改、审核、发起核实权限
2. 政策运营无线上抵押政策相关权限
3. 政策专员无线上抵押政策相关权限
4. 政策维护人拥有修改线上抵押政策权限
5. 小程序端详情页显示修改按钮且可正常使用"