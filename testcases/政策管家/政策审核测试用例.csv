用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【筛选项和列表】线上抵押业务类型显示验证,P0,"已登录系统;已进入政策审核页面","1. 在筛选项[业务类型]下拉框中观察选项内容，检查是否包含线上抵押
2. 在列表字段中观察[业务类型]列的显示内容，检查线上抵押数据的展示","1. 筛选项业务类型下拉框包含线上抵押选项
2. 列表字段业务类型列正确显示线上抵押数据"
TC002,【基本信息】审核页面信息显示验证,P0,"已登录系统;已进入政策审核页面;存在线上抵押审核单","1. 点击进入线上抵押政策的审核页面，观察基本信息区域的显示内容
2. 检查业务类型字段的显示值
3. 检查地区字段的显示格式
4. 检查修改人字段的显示格式","1. 基本信息区域正确显示所有必要字段
2. 业务类型显示为线上抵押
3. 地区按省市区格式正确显示
4. 修改人按角色-账号名称格式正确显示"
TC003,【适用地区】地区关联提示和字段剔除验证,P1,"已登录系统;已进入审核页面;存在地区关联冲突的审核单","1. 观察适用地区字段的地区关联提示信息显示
2. 检查上次不通过原因的显示情况
3. 验证字段剔除规则的执行效果
4. 在审核不通过时填写不通过原因，观察必填验证","1. 地区关联时显示提示文字xx区已与另一政策关联
2. 上一状态为审核不通过时正确显示上次不通过原因
3. 运营直接修改或审核通过的字段不在审核页面展示
4. 审核不通过时不通过原因字段必填验证生效"
TC004,【办理方式Tab】Tab显示和切换功能验证,P0,"已登录系统;已进入审核页面;存在多种办理方式的审核单","1. 观察办理方式Tab的显示顺序，检查六种办理方式的排列
2. 观察常用办理方式是否显示[常用]标签
3. 依次点击不同的Tab，观察页面内容切换效果","1. Tab按车管所办理、云车服办理、邮局办理、警邮平台办理、服务站办理、政务中心办理顺序显示
2. 常用办理方式Tab显示[常用]标签标识
3. 点击不同Tab时页面内容正确切换至对应办理方式的待审核政策内容"
TC005,【特殊情况】字段审核和不通过原因验证,P1,"已登录系统;已进入审核页面;存在特殊情况字段的审核单","1. 观察特殊情况字段的上次不通过原因显示
2. 验证字段剔除规则的执行情况
3. 选择审核不通过，在不通过原因字段中输入原因信息","1. 上一状态为审核不通过时正确显示上次不通过原因
2. 运营直接修改或审核通过的字段不在审核页面展示
3. 审核不通过时不通过原因字段必填且可正常输入"
TC006,【资料明细】原有资料展示和待审核内容格式验证,P0,"已登录系统;已进入审核页面;存在资料明细审核的审核单","1. 将鼠标悬停在[当前资料]蓝字上，观察悬浮显示效果
2. 观察待审核内容中资料明细的显示格式
3. 检查资料明细信息的完整性","1. 鼠标悬停时正确显示原有资料名称
2. 待审核内容按【文件名称】盖章类型:xx 数量:xx 资料模板:xx 是否为车管所特殊模板:xx格式显示
3. 资料明细信息显示完整，包含所有必要字段"
TC007,【抵押要求】审核流程和不通过原因验证,P1,"已登录系统;已进入审核页面;存在抵押要求字段的审核单","1. 观察抵押要求字段的上次不通过原因显示
2. 验证字段剔除规则的执行效果
3. 选择审核不通过，填写不通过原因","1. 上一状态为审核不通过时正确显示上次不通过原因
2. 运营直接修改或审核通过的字段不在审核页面展示
3. 审核不通过时不通过原因字段必填验证生效"
TC008,【收费情况】审核功能综合验证,P1,"已登录系统;已进入审核页面;存在收费情况字段的审核单","1. 观察收费情况字段的上次不通过原因显示
2. 验证字段剔除规则的执行情况
3. 进行审核操作，测试不通过原因的必填验证","1. 上一状态为审核不通过时正确显示上次不通过原因
2. 运营直接修改或审核通过的字段不在审核页面展示
3. 审核不通过时不通过原因字段必填且验证正常"
TC009,【抵押时效】审核流程完整性验证,P1,"已登录系统;已进入审核页面;存在抵押时效字段的审核单","1. 观察抵押时效字段的上次不通过原因显示
2. 验证字段剔除规则的执行效果
3. 完成审核操作，验证不通过原因填写要求","1. 上一状态为审核不通过时正确显示上次不通过原因
2. 运营直接修改或审核通过的字段不在审核页面展示
3. 审核不通过时不通过原因字段必填验证生效"
TC010,【办理场所要求】审核操作和规则验证,P1,"已登录系统;已进入审核页面;存在办理场所要求字段的审核单","1. 观察办理场所要求字段的上次不通过原因显示
2. 验证字段剔除规则的执行情况
3. 进行审核不通过操作，测试原因填写功能","1. 上一状态为审核不通过时正确显示上次不通过原因
2. 运营直接修改或审核通过的字段不在审核页面展示
3. 审核不通过时不通过原因字段必填且可正常填写"
TC011,【批量抵押】审核功能和规则验证,P1,"已登录系统;已进入审核页面;存在批量抵押字段的审核单","1. 观察批量抵押字段的上次不通过原因显示
2. 验证字段剔除规则的执行效果
3. 完成审核流程，验证不通过原因的处理","1. 上一状态为审核不通过时正确显示上次不通过原因
2. 运营直接修改或审核通过的字段不在审核页面展示
3. 审核不通过时不通过原因字段必填验证正常"
TC012,【字段剔除规则】无需审核自动流转验证,P0,"已登录系统;存在所有字段都被运营修改或审核通过的审核单","1. 观察审核单的状态变化情况
2. 检查审核页面是否还显示待审核字段
3. 验证自动流转为无需审核的逻辑","1. 审核单状态自动流转为无需审核
2. 审核页面不显示任何待审核字段
3. 系统自动完成流转，无需人工干预"