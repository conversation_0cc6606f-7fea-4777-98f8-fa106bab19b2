用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【页面结构】办理方式tab和搜索功能验证,P0,"已登录移动端系统;已进入变更日志页面","1. 观察页面中办理方式tab的显示内容，检查六种办理方式的展示
2. 观察常用办理方式tab是否显示[常用]标签
3. 在搜索框中观察默认提示内容
4. 在搜索框中输入带有中括号的字段名称，点击搜索按钮
5. 在搜索框中输入不带中括号的字段内容，点击搜索按钮","1. 页面正确显示六种办理方式的tab选项
2. 常用办理方式tab显示[常用]标签标识
3. 搜索框显示默认内容「请输入字段名称」
4. 带中括号的字段名称搜索成功，返回匹配结果
5. 不带中括号的字段内容搜索失败，无匹配结果"
TC002,【日志内容】基本信息显示验证,P0,"已登录移动端系统;已进入变更日志页面;存在变更记录","1. 观察变更日志记录的更新时间格式显示
2. 观察修改人信息的显示格式
3. 观察变更详情内容的展示情况","1. 更新时间按xxxx-xx-xx xx：xx格式正确显示
2. 修改人按角色名称-账号名称格式正确显示
3. 变更详情内容完整展示具体的变更信息"
TC003,【记录节点】PC端和小程序端修改记录验证,P0,"已登录移动端系统;存在PC端修改和小程序端修改的记录","1. 观察PC端修改记录的时间显示
2. 观察小程序端主动修改且PC全部审核通过的记录时间
3. 观察小程序端主动修改且PC部分字段审核不通过的记录显示
4. 观察小程序二次修改且字段与最初不同的记录显示
5. 观察小程序二次修改且字段与最初相同的记录显示","1. PC端修改记录显示修改的具体时间
2. 小程序端全部审核通过记录显示审核通过的时间
3. 部分字段审核不通过时只显示通过字段的日志，记录审核时间
4. 二次修改字段与最初不同时记录日志，显示审核时间
5. 二次修改字段与最初相同时不记录日志"
TC004,【记录节点】小程序核实记录验证,P1,"已登录移动端系统;存在小程序核实相关记录","1. 观察小程序核实记录的时间显示
2. 观察小程序核实且PC部分字段审核不通过的记录显示
3. 观察小程序二次核实且字段与最初不同的记录显示
4. 观察小程序二次核实且字段与最初相同的记录显示","1. 小程序核实记录显示核实修改的时间
2. 部分字段审核不通过时只显示通过字段的日志，记录审核时间
3. 二次核实字段与最初不同时记录日志，显示审核时间
4. 二次核实字段与最初相同时不记录日志"
TC005,【变更类型】常用办理方式变更验证,P0,"已登录移动端系统;存在常用办理方式变更记录","1. 观察车管所办理tab的常用办理方式变更日志内容
2. 观察云车服办理tab的常用办理方式变更日志内容
3. 检查变更日志的显示格式是否正确","1. 车管所办理tab日志显示【常用办理方式】车管所办理→云车服办理格式
2. 云车服办理tab日志显示【常用办理方式】车管所办理→云车服办理格式
3. 变更日志格式正确，箭头符号和内容显示准确"
TC006,【变更类型】使用地区变更验证,P0,"已登录移动端系统;存在使用地区变更记录","1. 观察地区数据从无到有的变更记录显示格式
2. 观察地区数据从有到无的变更记录显示格式
3. 观察地区数据从有到有的变更记录显示格式","1. 地区从无到有显示--→省份-城市-区县1、区县2···格式
2. 地区从有到无显示省份-城市-区县1、区县2···→--格式
3. 地区从有到有显示省份-城市-区县1、区县2···→省份-城市-区县1、区县2···格式"
TC007,【变更类型】非资料明细字段变更验证,P1,"已登录移动端系统;存在特殊情况、抵押要求、收费情况等字段变更记录","1. 观察修改前字段非空的变更记录显示格式
2. 观察修改前字段为空的变更记录显示格式
3. 检查特殊情况字段变更的记录格式
4. 检查抵押要求字段变更的记录格式
5. 检查收费情况、抵押时效、办理场所要求、批量抵押字段变更的记录格式","1. 修改前字段非空时显示字段名称：**→**格式
2. 修改前字段为空时显示字段名称：字段内容格式
3. 特殊情况字段变更按非资料明细字段格式正确显示
4. 抵押要求字段变更按非资料明细字段格式正确显示
5. 其他模块字段变更均按非资料明细字段格式正确显示"
TC008,【变更类型】资料明细字段变更验证,P0,"已登录移动端系统;存在资料明细相关的变更记录","1. 观察新增一份资料的变更记录显示格式
2. 观察删除一份资料的变更记录显示格式
3. 观察修改现有资料内容的变更记录显示格式
4. 观察修改自定义资料信息的变更记录显示格式","1. 新增资料显示【资料模块名称】：新增 资料名称 盖章类型 数量 模板 是否是车管所特殊模板格式
2. 删除资料显示【资料模块名称】：删除 资料名称 盖章类型 数量 模板 是否是车管所特殊模板格式
3. 修改现有资料显示【公司备案】：抵押委托书样本 鲜章→电子章 1份→2份 模板A.pdf→模板B.pdf 是车管所特殊模板→非车管所特殊模板格式
4. 修改自定义资料显示【公司备案】：自定义资料名称 资料A→资料B 鲜章→电子章 1份→2份 模板A.pdf→模板B.pdf 是车管所特殊模板→非车管所特殊模板格式"
TC009,【变更类型】线上抵押办理能力字段变更验证,P1,"已登录移动端系统;存在当前机构是否可以办理线上抵押字段变更记录","1. 观察字段从[是]变为[否]时的日志记录内容
2. 观察字段从[否]变为[是]时的日志记录内容
3. 检查字段从[是]变为[否]时其他子级字段是否在日志中体现
4. 检查字段从[否]变为[是]时其他子级字段是否在日志中体现","1. 字段从[是]变为[否]时日志中只记录该字段的变动
2. 字段从[否]变为[是]时日志中记录该字段和其他子级的变动
3. 字段从[是]变为[否]时其他子级字段不在日志中体现
4. 字段从[否]变为[是]时其他子级字段的变动都在日志中体现"
TC010,【移动端政策核实】基本信息显示验证,P0,"已登录移动端系统;已进入政策核实页面","1. 观察基本信息中业务类型字段的显示内容
2. 观察基本信息中主体类型字段的显示内容
3. 观察备注模块的显示情况
4. 观察适用地区的显示内容","1. 业务类型正确显示为线上抵押
2. 主体类型根据实际情况正确显示
3. 有备注时显示运营发起核实时填写的备注，无备注时不展示本模块
4. 适用地区正确显示政策适用的地区范围"
TC011,【移动端政策核实】特殊情况字段验证,P0,"已登录移动端系统;已进入政策核实页面","1. 在[特殊情况]文本输入框中输入内容，观察输入效果
2. 在[当前机构是否可以办理线上抵押]字段选择[是]，观察页面字段显示变化
3. 在[当前机构是否可以办理线上抵押]字段选择[否]，观察页面字段显示变化
4. 检查当前机构是否可以办理线上抵押字段的默认值","1. 特殊情况文本框正常输入内容，支持文本编辑
2. 选择[是]时联动展示其他相关字段
3. 选择[否]时不展示其他字段
4. 当前机构是否可以办理线上抵押字段无默认值"
TC012,【移动端政策核实】资料明细验证,P0,"已登录移动端系统;当前机构可以办理线上抵押","1. 在私户车线上抵押资料选择中观察新增的三个资料选项
2. 选择[代理人身份证原件]资料，观察资料列表变化
3. 选择[抵押人委托书]资料，观察资料列表变化
4. 选择[情况说明]资料，观察资料列表变化
5. 选择[抵押人本人到场]资料，观察联动字段变化
6. 观察资料列表中各字段的显示内容
7. 检查默认资料的删除权限","1. 新增三个资料选项正确显示在资料选择列表中
2. 代理人身份证原件成功添加到资料列表
3. 抵押人委托书成功添加到资料列表
4. 情况说明成功添加到资料列表
5. 选择抵押人本人到场后联动影响抵押人是否需要到场和抵押人不到场渠道费字段
6. 资料列表正确显示序号、资料名称、盖章类型、数量、资料模版、是否为车管所特殊模版、操作字段
7. 机动车登记证书、代理人身份证原件为默认资料，不可删除"
TC013,【移动端政策核实】抵押要求字段验证,P1,"已登录移动端系统;已进入抵押要求模块","1. 在[抵押人是否需要到场]字段中选择是或否，观察选择效果
2. 在各文本输入框字段中输入内容，观察输入效果
3. 检查所有单选项字段的选择功能
4. 验证各字段的数据保存功能","1. 抵押人是否需要到场单选项正常选择是或否
2. 脱审对抵押有无影响、抵押资料填写要求、黄牌车/货车特殊情况备注、有违章对抵押有无影响等文本框正常输入
3. 车辆抵押状态下是否可以办理营运证、合同上是否可以盖合同专用章、营运车辆是否可以抵押等单选项正常选择
4. 各字段数据正确保存"
TC014,【移动端政策核实】收费情况字段验证,P1,"已登录移动端系统;已进入收费情况模块","1. 在各费用字段中输入纯数字，观察输入效果
2. 在各费用备注字段中输入内容，观察输入效果
3. 检查所有费用字段的输入限制
4. 验证费用和费用备注的关联显示","1. 车管所办理抵押的工本费、抵押人不到场渠道费等字段正常输入纯数字
2. 各费用备注字段正常输入文本内容
3. 费用字段只支持纯数字输入，其他字符输入受限
4. 费用和费用备注正确关联显示"
TC015,【移动端政策核实】抵押时效字段验证,P1,"已登录移动端系统;已进入抵押时效模块","1. 在办理时机相关的单选项字段中进行选择，观察选择效果
2. 在预约相关的文本输入框中输入内容，观察输入效果
3. 在出证时效相关字段中进行操作，观察功能效果
4. 检查约号方式的枚举选择功能
5. 检查凭证样式的文件上传功能","1. 新车上牌和抵押是否可以一起办理、二手车过户和抵押是否可以一起办理等单选项正常选择
2. 抵押需提前多久预约、约号流程说明等文本框正常输入
3. 收到资料齐全后多久可以出证、最长出证时间等字段正常操作
4. 约号方式枚举选择功能正常
5. 凭证样式文件上传功能正常"
TC016,【移动端政策核实】办理场所要求和批量抵押验证,P1,"已登录移动端系统;已进入相关模块","1. 在办理场所要求的文本输入框中输入内容，观察输入效果
2. 在办理场所要求的单选项字段中进行选择，观察选择效果
3. 在批量抵押的单选项字段中进行选择，观察选择效果
4. 在批量抵押的文本输入框中输入内容，观察输入效果","1. 户籍为县城的抵押权人是否可到市区上牌和抵押、办理地点具体名称、县城上牌的车辆是否可在市区抵押等文本框正常输入
2. 办理机构是否可现场收费单选项正常选择是或否
3. 是否可批量抵押单选项正常选择是或否
4. 一人一次最多办理抵押单的数量、一人一天最大办理次数、批量车当天可以出本的上限台数等文本框正常输入"