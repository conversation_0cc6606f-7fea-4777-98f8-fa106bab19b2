用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【填写基本信息】业务类型和核实类型选择验证,P0,"已登录系统;已进入批量核实页面","1. 在业务类型下拉框中观察是否包含[线上抵押]选项，选择线上抵押
2. 在核实类型中选择[月度核实]，观察表单名称字段的变化
3. 在核实类型中选择[临时核实]，观察表单名称字段的变化
4. 在表单名称输入框中输入超过50个字符的内容，观察系统提示","1. 业务类型下拉框成功包含线上抵押选项，选择正常
2. 选择月度核实时表单名称自动填充为「月度核实」且可二次编辑
3. 选择临时核实时表单名称可自由填写，最大50个字符
4. 输入超过50个字符时系统提示字符长度限制"
TC002,【填写基本信息】地区添加和批量导入功能验证,P0,"已登录系统;已进入批量核实页面","1. 点击添加地区功能，观察地区选择界面的展示方式
2. 尝试勾选全国选项，观察下级地区的联动变化
3. 尝试勾选某个省份，观察该省下城市的联动变化
4. 点击批量导入功能，上传包含青岛市的excel文件，观察导入结果
5. 上传包含错误城市名称的excel文件，观察系统提示","1. 地区选择界面全国省份与城市铺开展示，支持多选
2. 勾选全国时所有省份和城市被选中，精确到二级城市
3. 勾选省份时该省下所有城市被选中
4. 导入青岛市成功，自动勾选青岛市下所有区县
5. 错误城市名称导入失败，弹出提示「xx、xx导入失败请重新导入！」"
TC003,【填写基本信息】主体类型和办理方式配置验证,P1,"已登录系统;已进入批量核实页面","1. 在主体类型字段中观察是否支持多选功能
2. 在[是否选择常用办理方式]字段选择[是]，观察关联的办理方式
3. 在[是否选择常用办理方式]字段选择[否]，观察关联的办理方式
4. 配置期望反馈时间，观察时间精确度要求","1. 主体类型字段支持多选，可选择多个主体类型
2. 选择[是]时关联常用办理方式
3. 选择[否]时关联六种办理方式
4. 期望反馈时间必填且精确到日，以当日24:00为准"
TC004,【选择核实字段】基本信息和资料明细字段验证,P0,"已登录系统;已完成基本信息填写;已进入选择核实字段页面","1. 在基本信息模块中观察可选择的字段内容
2. 在资料明细模块中选择[全部资料]选项，观察包含的资料范围
3. 在资料明细模块中选择[个别文件]选项，观察下拉选择内容
4. 测试个别文件的清空功能","1. 基本信息模块包含特殊情况、适用地区字段
2. 全部资料选项包括全部所选资料+自定义资料
3. 个别文件提供下拉选择，显示可选的具体文件
4. 个别文件支持清空操作，清空后恢复初始状态"
TC005,【选择核实字段】抵押要求和收费情况字段验证,P1,"已登录系统;已进入选择核实字段页面","1. 在抵押要求模块中观察包含的字段列表
2. 在收费情况模块中观察包含的字段列表
3. 检查抵押要求字段是否包含抵押人是否需要到场、脱审对抵押有无影响等9个字段
4. 检查收费情况字段是否包含车管所办理抵押的工本费等相关费用字段","1. 抵押要求模块正确显示所有相关字段选项
2. 收费情况模块正确显示所有费用相关字段选项
3. 抵押要求包含抵押人是否需要到场、抵押人不到场的渠道费、脱审对抵押有无影响、车辆抵押状态下是否可以办理营运证、抵押资料填写要求、黄牌车/货车特殊情况备注、有违章对抵押有无影响、合同上是否可以盖合同专用章、营运车辆是否可以抵押9个字段
4. 收费情况包含车管所办理抵押的工本费、抵押人不到场渠道费、柜台非官方收费、买号费、加急归档费用、车管所办理抵押的渠道费、牛工代办费、提档费、归档期是否可以加急等字段"
TC006,【选择核实字段】抵押时效和办理场所字段验证,P1,"已登录系统;已进入选择核实字段页面","1. 在抵押时效模块中观察包含的字段列表
2. 在办理场所要求模块中观察包含的字段列表
3. 在批量抵押模块中观察包含的字段列表
4. 检查抵押时效字段的完整性","1. 抵押时效模块正确显示所有时效相关字段选项
2. 办理场所要求模块正确显示所有场所相关字段选项
3. 批量抵押模块正确显示所有批量相关字段选项
4. 抵押时效包含新车上牌和抵押是否可以一起办理、二手车过户和抵押是否可以一起办理、收到资料齐全后多久可以出证、抵押需提前多久预约、约号流程说明、新车上牌后多久可抵押、二手车过户后多久可抵押、抵押是否需要预约、约号方式、最长出证时间、办理回执类型、凭证样式等字段"
TC007,【选择核实字段】全部字段选择和附件上传验证,P0,"已登录系统;已进入选择核实字段页面","1. 选择全部字段选项，观察所有模块字段的选中状态
2. 在附件上传区域尝试上传图片格式文件，观察上传结果
3. 在附件上传区域尝试上传pdf格式文件，观察上传结果
4. 在备注文本框中输入备注信息
5. 观察页面底部按钮的显示情况","1. 选择全部字段时所有模块的字段均被选中
2. 图片格式文件上传成功，支持预览
3. pdf格式文件上传成功，支持查看
4. 备注信息输入正常，支持文本内容
5. 页面底部显示取消、上一步、下一步、提交四个按钮"
TC008,【资料明细】临时核实和月度核实差异验证,P1,"已登录系统;已选择不同核实类型","1. 选择临时核实类型，在个别文件中选择当前政策没有维护的文件，观察小程序端核实页面的数据生成
2. 选择月度核实类型，在个别文件中选择当前政策没有维护的文件，观察小程序端核实页面的数据展示
3. 对比两种核实类型在处理未维护文件时的不同表现","1. 临时核实选择未维护文件时，小程序端核实页面生成新的资料数据
2. 月度核实选择未维护文件时，小程序端核实页面只展示当前政策已维护的资料数据
3. 两种核实类型在处理未维护文件时表现不同，符合业务规则"
TC009,【完成页面】信息显示和操作按钮验证,P0,"已登录系统;已完成核实字段选择;已进入完成页面","1. 观察完成页面显示的核实类型信息
2. 观察完成页面显示的表单名称信息
3. 观察完成页面显示的期望反馈时间信息
4. 观察完成页面显示的主体类型信息
5. 观察页面底部的操作按钮","1. 完成页面正确显示之前选择的核实类型
2. 完成页面正确显示之前填写的表单名称
3. 完成页面正确显示之前设置的期望反馈时间
4. 完成页面正确显示之前选择的主体类型
5. 页面底部显示[再次核实]和[返回]两个按钮"
TC010,【月度核实流程】完整流程验证,P0,"已登录系统;已进入政策信息库页面","1. 在政策信息库页面点击[批量核实]按钮
2. 选择业务类型为线上抵押，选择核实类型为月度核实，点击[下一步]
3. 在选择核实字段页面勾选全部字段
4. 上传附件文件城市名称为青岛市，点击[下一步]
5. 在自定义字段页面新增字段1为输入框，字段2为下拉选择包含选项A和选项B
6. 观察政策核实页面的数据新增情况","1. 成功进入批量核实页面，页面加载正常
2. 业务类型和核实类型选择成功，进入下一步
3. 全部字段勾选成功，字段选择完整
4. 附件上传成功，青岛市数据处理正确
5. 自定义字段新增成功，字段1为输入框，字段2为下拉选择
6. 政策核实页面新增一条数据，核实单包含所有字段"
TC011,【临时核实流程】完整流程验证,P0,"已登录系统;已进入政策信息库页面","1. 在政策信息库页面点击[批量核实]按钮
2. 选择业务类型为线上抵押，选择核实类型为临时核实，点击[下一步]
3. 在选择核实字段页面勾选全部字段
4. 上传附件文件城市名称为青岛市，点击[下一步]
5. 在自定义字段页面新增字段1为输入框，字段2为下拉选择包含选项A和选项B
6. 观察政策核实页面的数据新增情况","1. 成功进入批量核实页面，页面加载正常
2. 业务类型和核实类型选择成功，进入下一步
3. 全部字段勾选成功，字段选择完整
4. 附件上传成功，青岛市数据处理正确
5. 自定义字段新增成功，字段1为输入框，字段2为下拉选择
6. 政策核实页面新增一条数据，核实单包含所有字段"
TC012,【边界场景】字段选择和数据验证,P2,"已登录系统;已进入批量核实页面","1. 在表单名称中输入空内容，尝试进入下一步
2. 不选择任何地区，尝试进入下一步
3. 不选择任何主体类型，尝试进入下一步
4. 不设置期望反馈时间，尝试进入下一步
5. 在选择核实字段页面不选择任何字段，尝试提交","1. 表单名称为空时系统提示必填项错误，无法进入下一步
2. 未选择地区时系统提示必须选择地区，无法进入下一步
3. 未选择主体类型时系统提示必须选择主体类型，无法进入下一步
4. 未设置期望反馈时间时系统提示必填项错误，无法进入下一步
5. 未选择任何核实字段时系统提示必须选择核实字段，无法提交"
TC013,【办理方式联动】地区政策关联验证,P1,"已登录系统;已选择特定地区;地区政策中缺少某些办理方式","1. 选择[是否选择常用办理方式]为[否]，观察关联的六种办理方式
2. 检查所选地区政策中实际支持的办理方式
3. 观察核实单中办理方式的展示情况
4. 验证不支持的办理方式是否在核实单中隐藏","1. 选择[否]时系统关联六种办理方式选项
2. 系统正确识别所选地区政策支持的办理方式
3. 核实单中只展示地区政策支持的办理方式
4. 地区政策不支持的办理方式在核实单中不展示，符合联动规则"