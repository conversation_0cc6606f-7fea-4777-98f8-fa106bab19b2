用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【基本信息】页面信息显示验证,P0,"已登录系统;已进入二次修改页面","1. 观察页面顶部[业务类型]字段的显示内容
2. 观察页面顶部[地区]字段的显示内容
3. 观察页面顶部[修改人]字段的显示内容","1. 业务类型正确显示为线上抵押
2. 地区正确显示省市区信息
3. 修改人正确显示角色-账号名称格式"
TC002,【适用地区】地区关联提示验证,P1,"已登录系统;已进入二次修改页面","1. 观察当前选择的适用地区是否显示关联提示文字
2. 检查页面是否显示「xx区已与另一政策关联」提示
3. 观察小程序端核实时添加地区的处理","1. 当前选择的适用地区已与其他同政策关联时不展示提示文字
2. 页面不显示「xx区已与另一政策关联」提示
3. 小程序端核实时可正常添加地区"
TC003,【适用地区】新增地区处理验证,P0,"已登录系统;小程序端核实时添加了新地区","1. 观察新增地区的显示格式
2. 在新增地区已与另一该业务类型政策关联的情况下，点击提交表单
3. 在弹窗中点击确定按钮，观察系统处理结果
4. 观察地区政策更新后的状态","1. 新增地区显示为【适用地区】新增区县1、区县2、区县3格式
2. 提交表单时弹窗提示「XX地区已关联其他政策，是否确定更新绑定关系？」
3. 点击确定后系统提示「xx区、xx区、xx区、xx区、xx区已关联另一政策，是否确定更新绑定关系？」
4. 确定后该地区该业务类型的政策成功更新"
TC004,【适用地区】审核相关字段验证,P1,"已登录系统;当前审核单上一个状态为审核不通过","1. 观察[上次不通过原因]字段的显示内容
2. 在当前字段审核结果为不通过时，观察[审核不通过原因]字段状态
3. 验证审核不通过原因字段的必填验证","1. 上次不通过原因正确显示运营上次审核不通过该字段时填写的不通过原因
2. 当前字段审核结果为不通过时，审核不通过原因字段为必填状态
3. 审核不通过原因未填写时系统提示必填"
TC005,【适用地区】字段剔除规则验证,P0,"已登录系统;运营已直接修改字段或审核通过其他人修改","1. 在运营直接修改该字段的情况下，观察审核页面字段显示
2. 在运营审核通过了其他人提交的该字段修改的情况下，观察审核页面字段显示
3. 在所有字段都无需审核的情况下，观察审核流转状态","1. 运营直接修改该字段后，审核页面不展示该字段
2. 运营审核通过了其他人提交的该字段修改后，审核页面不展示该字段
3. 所有字段都无需审核时，该条审核自动流转为无需审核状态"
TC006,【适用地区】联动字段处理验证,P1,"已登录系统;存在抵押人本人到场联动字段","1. 观察抵押人本人到场联动字段的审核不通过原因显示位置
2. 检查抵押人是否需要到场字段的审核不通过原因显示
3. 检查抵押人不到场渠道费字段的审核不通过原因显示","1. 联动字段的审核不通过原因展示到抵押人本人到场字段
2. 抵押人是否需要到场字段不展示审核不通过原因
3. 抵押人不到场渠道费字段不展示审核不通过原因"
TC007,【不同Tab】Tab切换功能验证,P0,"已登录系统;已进入二次修改页面","1. 点击[车管所办理]Tab，观察页面内容切换
2. 点击[云车服办理]Tab，观察页面内容切换
3. 点击[邮局办理]Tab，观察页面内容切换
4. 点击[警邮平台办理]Tab，观察页面内容切换
5. 点击[服务站办理]Tab，观察页面内容切换
6. 点击[政务中心办理]Tab，观察页面内容切换
7. 观察常用办理方式的标签显示","1. 车管所办理Tab切换成功，显示对应办理方式的待审核政策内容
2. 云车服办理Tab切换成功，显示对应办理方式的待审核政策内容
3. 邮局办理Tab切换成功，显示对应办理方式的待审核政策内容
4. 警邮平台办理Tab切换成功，显示对应办理方式的待审核政策内容
5. 服务站办理Tab切换成功，显示对应办理方式的待审核政策内容
6. 政务中心办理Tab切换成功，显示对应办理方式的待审核政策内容
7. 常用办理方式正确显示「常用」标签"
TC008,【特殊情况】审核相关字段验证,P1,"已登录系统;当前审核单上一个状态为审核不通过","1. 观察特殊情况模块的[上次不通过原因]字段显示
2. 在当前字段审核结果为不通过时，观察[审核不通过原因]字段状态
3. 验证审核不通过原因字段的必填验证","1. 上次不通过原因正确显示运营上次审核不通过该字段时填写的不通过原因
2. 当前字段审核结果为不通过时，审核不通过原因字段为必填状态
3. 审核不通过原因未填写时系统提示必填"
TC009,【特殊情况】字段剔除规则验证,P0,"已登录系统;运营已直接修改字段或审核通过其他人修改","1. 在运营直接修改特殊情况字段的情况下，观察审核页面字段显示
2. 在运营审核通过了其他人提交的特殊情况字段修改的情况下，观察审核页面字段显示","1. 运营直接修改该字段后，审核页面不展示该字段
2. 运营审核通过了其他人提交的该字段修改后，审核页面不展示该字段"
TC010,【资料明细】原有资料展示验证,P1,"已登录系统;已进入资料明细审核页面","1. 将鼠标放置到「当前资料」蓝字上，观察悬浮效果
2. 观察悬浮展示的原有资料名称内容
3. 移开鼠标，观察悬浮框消失效果","1. 鼠标放置到「当前资料」蓝字时正确触发悬浮效果
2. 悬浮框正确展示原有资料名称
3. 移开鼠标后悬浮框正常消失"
TC011,【资料明细】待审核内容展示验证,P0,"已登录系统;待审核内容包含资料明细","1. 观察待审核内容中资料明细的显示格式
2. 检查文件名称的显示内容
3. 检查盖章类型、数量、资料模板、是否为车管所特殊模板字段的显示","1. 待审核内容正确显示资料明细信息
2. 文件名称正确显示在方括号内
3. 显示格式为【文件名称】盖章类型:xx 数量:xx 资料模板:xx 是否为车管所特殊模板:xx"
TC012,【资料明细】审核相关字段验证,P1,"已登录系统;当前审核单上一个状态为审核不通过","1. 观察资料明细模块的[上次不通过原因]字段显示
2. 观察[审核不通过原因]在文件卡片上方的显示位置","1. 上次不通过原因正确显示运营上次审核不通过该字段时填写的不通过原因
2. 审核不通过原因正确显示在文件卡片上方"
TC013,【资料明细】字段剔除规则验证,P0,"已登录系统;运营已直接修改字段或审核通过其他人修改","1. 在运营直接修改资料明细字段的情况下，观察审核页面字段显示
2. 在运营审核通过了其他人提交的资料明细字段修改的情况下，观察审核页面字段显示","1. 运营直接修改该字段后，审核页面不展示该字段
2. 运营审核通过了其他人提交的该字段修改后，审核页面不展示该字段"
TC014,【抵押要求】字段剔除规则验证,P0,"已登录系统;运营已直接修改字段或审核通过其他人修改","1. 在运营直接修改抵押要求字段的情况下，观察审核页面字段显示
2. 在运营审核通过了其他人提交的抵押要求字段修改的情况下，观察审核页面字段显示","1. 运营直接修改该字段后，审核页面不展示该字段
2. 运营审核通过了其他人提交的该字段修改后，审核页面不展示该字段"
TC015,【抵押要求】联动字段处理验证,P1,"已登录系统;存在抵押人本人到场联动字段","1. 观察抵押人本人到场联动字段的审核不通过原因显示
2. 检查抵押人是否需要到场字段的审核不通过原因显示","1. 联动字段的审核不通过原因展示到抵押人本人到场字段
2. 抵押人是否需要到场字段不展示审核不通过原因"
TC016,【收费情况】字段剔除规则验证,P0,"已登录系统;运营已直接修改字段或审核通过其他人修改","1. 在运营直接修改收费情况字段的情况下，观察审核页面字段显示
2. 在运营审核通过了其他人提交的收费情况字段修改的情况下，观察审核页面字段显示","1. 运营直接修改该字段后，审核页面不展示该字段
2. 运营审核通过了其他人提交的该字段修改后，审核页面不展示该字段"
TC017,【收费情况】归档期加急联动字段验证,P1,"已登录系统;存在归档期是否可以加急联动字段","1. 观察归档期是否可以加急字段的审核不通过原因显示
2. 检查加急归档费用字段的审核不通过原因显示","1. 归档期是否可以加急字段展示审核不通过原因
2. 加急归档费用字段不展示审核不通过原因"
TC018,【收费情况】抵押人到场联动字段验证,P1,"已登录系统;存在抵押人本人到场联动字段","1. 观察抵押人本人到场联动字段的审核不通过原因显示
2. 检查抵押人不到场渠道费字段的审核不通过原因显示","1. 联动字段的审核不通过原因展示到抵押人本人到场字段
2. 抵押人不到场渠道费字段不展示审核不通过原因"
TC019,【抵押时效】字段剔除规则验证,P0,"已登录系统;运营已直接修改字段或审核通过其他人修改","1. 在运营直接修改抵押时效字段的情况下，观察审核页面字段显示
2. 在运营审核通过了其他人提交的抵押时效字段修改的情况下，观察审核页面字段显示","1. 运营直接修改该字段后，审核页面不展示该字段
2. 运营审核通过了其他人提交的该字段修改后，审核页面不展示该字段"
TC020,【抵押时效】预约相关联动字段验证,P1,"已登录系统;存在抵解押是否需要预约联动字段","1. 观察抵解押是否需要预约字段的审核不通过原因显示
2. 检查抵押需提前多久预约字段的审核不通过原因显示
3. 检查约号流程说明字段的审核不通过原因显示
4. 检查约号方式字段的审核不通过原因显示","1. 抵解押是否需要预约字段展示审核不通过原因
2. 抵押需提前多久预约字段不展示审核不通过原因
3. 约号流程说明字段不展示审核不通过原因
4. 约号方式字段不展示审核不通过原因"
TC021,【抵押时效】出证时间联动字段验证,P1,"已登录系统;存在最长出证时间联动字段","1. 观察最长出证时间字段的审核不通过原因显示
2. 检查办理回执类型字段的审核不通过原因显示
3. 检查凭证样式字段的审核不通过原因显示","1. 最长出证时间字段展示审核不通过原因
2. 办理回执类型字段不展示审核不通过原因
3. 凭证样式字段不展示审核不通过原因"
TC022,【办理场所要求】字段剔除规则验证,P0,"已登录系统;运营已直接修改字段或审核通过其他人修改","1. 在运营直接修改办理场所要求字段的情况下，观察审核页面字段显示
2. 在运营审核通过了其他人提交的办理场所要求字段修改的情况下，观察审核页面字段显示","1. 运营直接修改该字段后，审核页面不展示该字段
2. 运营审核通过了其他人提交的该字段修改后，审核页面不展示该字段"
TC023,【审核流程】完整审核流程验证,P0,"已登录系统;存在待审核字段","1. 对所有待审核字段进行审核通过操作，观察审核结果
2. 对部分字段进行审核不通过操作并填写不通过原因，观察审核结果
3. 提交审核结果，观察系统处理状态
4. 观察审核完成后的页面状态变化","1. 审核通过的字段正确标记为通过状态
2. 审核不通过的字段正确标记为不通过状态，不通过原因必填且保存成功
3. 提交审核结果后系统正确处理审核状态
4. 审核完成后页面状态正确更新"