用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【特殊情况】字段显示和联动验证,P0,"已登录系统;已进入政策详情页","1. 观察[特殊情况]字段的显示内容，检查换行效果保持
2. 观察[当前机构是否可以办理线上抵押]字段选择[是]时的页面变化
3. 观察[当前机构是否可以办理线上抵押]字段选择[否]时的页面变化
4. 检查特殊情况字段为空时的显示内容","1. 特殊情况字段正确显示内容，编辑页的换行效果在详情页保持
2. 选择[是]时联动展示其他相关字段
3. 选择[否]时不展示其他字段
4. 特殊情况为空时显示「待调研」"
TC002,【资料明细】私户车和公户车资料显示验证,P0,"已登录系统;已进入政策详情页;当前机构可以办理线上抵押","1. 观察私户车线上抵押资料列表的显示内容，检查序号、资料名称、盖章类型、数量、资料模版、是否为车管所特殊模版字段
2. 检查机动车登记证书、代理人身份证原件是否显示为默认资料
3. 观察公户车线上抵押资料列表的显示内容
4. 检查新增的三个资料（代理人身份证原件、抵押人委托书、情况说明）的显示顺序","1. 私户车资料列表正确显示所有字段信息，数据完整
2. 机动车登记证书、代理人身份证原件显示为默认资料，无删除操作
3. 公户车资料列表显示内容与私户车相同
4. 新增三个资料的优先级仅高于自定义资料，排序正确"
TC003,【资料明细】复制资料和下载模版功能验证,P1,"已登录系统;已进入政策详情页;资料列表中包含自定义资料","1. 点击[复制资料]按钮，检查复制到剪贴板的内容
2. 将复制的内容粘贴到文本编辑器中，观察资料顺序和内容格式
3. 检查盖章类型为无需盖章的资料复制内容
4. 点击[下载模板]按钮，观察下载的压缩包文件
5. 点击已上传资料模版的资料名称，观察页面跳转","1. 成功复制资料名称、盖章类型、份数三列内容到剪贴板
2. 粘贴内容的资料顺序正确，格式规范
3. 无需盖章的资料只复制资料名称和份数
4. 下载的压缩包文件名为线上抵押，包含所有模板文件
5. 点击资料模版名称跳转到新页面查看模板"
TC004,【资料明细】空值处理验证,P2,"已登录系统;已进入政策详情页;存在字段值为空的资料","1. 观察盖章类型字段为空的资料显示内容
2. 观察资料模版字段为空的资料显示内容
3. 观察是否为车管所特殊模版字段为空的资料显示内容
4. 观察资料特殊要求说明字段为空时的显示内容","1. 盖章类型为空时显示「待调研」
2. 资料模版为空时显示「待调研」
3. 是否为车管所特殊模版为空时显示「待调研」
4. 资料特殊要求说明为空时显示「待调研」"
TC005,【抵押要求】抵押人到场联动和基础字段验证,P0,"已登录系统;已进入政策详情页","1. 观察抵押人本人到场资料已添加时[抵押人是否需要到场]字段的状态
2. 观察抵押人本人到场资料未添加时[抵押人是否需要到场]字段的状态
3. 检查[车辆抵押状态下是否可以办理营运证]、[合同上是否可以盖合同专用章]、[营运车辆是否可以抵押]字段的选项
4. 观察[脱审对抵押有无影响]、[抵押资料填写要求]、[有违章对抵押有无影响]、[黄牌车/货车特殊情况备注]字段的默认内容","1. 抵押人本人到场资料已添加时，抵押人是否需要到场=是且不可修改
2. 抵押人本人到场资料未添加时，抵押人是否需要到场=否
3. 三个单选字段均显示是/否选项，可正常选择
4. 四个输入框字段显示对应的默认提示内容"
TC006,【抵押要求】空值处理验证,P2,"已登录系统;已进入政策详情页;存在字段值为空的抵押要求","1. 观察脱审对抵押有无影响字段为空时的显示内容
2. 观察有违章对抵押有无影响字段为空时的显示内容
3. 观察抵押资料填写要求字段为空时的显示内容
4. 观察黄牌车/货车特殊情况备注字段为空时的显示内容","1. 脱审对抵押有无影响为空时显示「待调研」
2. 有违章对抵押有无影响为空时显示「待调研」
3. 抵押资料填写要求为空时显示「待调研」
4. 黄牌车/货车特殊情况备注为空时显示「待调研」"
TC007,【收费情况】费用字段和联动规则验证,P0,"已登录系统;已进入政策详情页","1. 观察车管所办理抵押的工本费、柜台非官方收费、买号费、车管所办理抵押的渠道费、牛工代办费、提档费字段的显示格式
2. 检查抵押人本人到场资料已添加时[抵押人不到场渠道费]字段的状态
3. 检查抵押人本人到场资料未添加时[抵押人不到场渠道费]字段的状态
4. 观察归档期是否可以加急=是时[加急归档费用]字段的显示
5. 观察归档期是否可以加急=否时[加急归档费用]字段的显示","1. 所有费用字段支持纯数字+费用备注的格式显示
2. 抵押人本人到场资料已添加时，抵押人不到场渠道费可填写
3. 抵押人本人到场资料未添加时，抵押人不到场渠道费=无需到场且不可修改
4. 归档期是否可以加急=是时，展示加急归档费用字段且可填写
5. 归档期是否可以加急=否时，隐藏加急归档费用字段"
TC008,【收费情况】空值处理验证,P2,"已登录系统;已进入政策详情页;存在费用字段为空的收费情况","1. 观察车管所办理抵押的工本费为空时的显示内容
2. 观察车管所办理抵押的渠道费为空时的显示内容
3. 观察抵押人不到场渠道费为空时的显示内容
4. 观察牛工代办费、柜台非官方收费、提档费、买号费、加急归档费用为空时的显示内容","1. 车管所办理抵押的工本费为空时，费用显示--，费用备注显示「待调研」
2. 车管所办理抵押的渠道费为空时，费用显示--，费用备注显示「待调研」
3. 抵押人不到场渠道费为空时，费用显示--，费用备注显示「待调研」
4. 所有费用字段为空时，费用显示--，费用备注显示「待调研」"
TC009,【抵押时效】基础时效字段和联动规则验证,P0,"已登录系统;已进入政策详情页","1. 观察[新车上牌和抵押是否可以一起办理]、[二手车过户和抵押是否可以一起办理]、[新车上牌后多久可抵押]、[抵押是否需要预约]字段的选项
2. 观察[收到资料齐全后多久可以出证]、[二手车过户后多久可抵押]、[最长出证时间]字段的默认内容
3. 检查抵押是否需要预约=是时相关字段的显示
4. 检查抵押是否需要预约=否时相关字段的显示","1. 四个单选字段均显示是/否选项，可正常选择
2. 三个输入框字段显示对应的默认提示内容
3. 抵押是否需要预约=是时，展示抵押需提前多久预约、约号流程说明、约号方式字段
4. 抵押是否需要预约=否时，不展示抵押需提前多久预约、约号流程说明、约号方式字段"
TC010,【抵押时效】约号方式和回执类型验证,P1,"已登录系统;已进入政策详情页;抵押是否需要预约=是","1. 观察约号方式字段的枚举值选项内容
2. 检查最长出证时间>1时办理回执类型和凭证样式字段的显示
3. 检查最长出证时间=1时办理回执类型和凭证样式字段的显示
4. 观察办理回执类型的枚举值选项内容","1. 约号方式包含现场取号机取号、【佛山交管】公众号、【惠警办】小程序、【i莞家】公众号、【广州交警】公众号、【善美村居】小程序、【苏州交警】公众号、【上海交警】APP等8个选项
2. 最长出证时间>1时，展示办理回执类型和凭证样式字段且为必填
3. 最长出证时间=1时，不展示办理回执类型和凭证样式字段
4. 办理回执类型包含警邮系统完成凭证、申请表照片、业务授理凭证、应收材料清单、云车服系统截图等5个选项"
TC011,【抵押时效】空值处理验证,P2,"已登录系统;已进入政策详情页;存在字段值为空的抵押时效","1. 观察二手车过户后多久可抵押字段为空时的显示内容
2. 观察收到资料齐全后多久可以出证字段为空时的显示内容
3. 观察抵押需提前多久预约字段为空时的显示内容
4. 观察约号方式、约号流程说明、最长出证时间字段为空时的显示内容","1. 二手车过户后多久可抵押为空时显示「待调研」
2. 收到资料齐全后多久可以出证为空时显示「待调研」
3. 抵押需提前多久预约为空时显示「待调研」
4. 约号方式、约号流程说明、最长出证时间为空时均显示「待调研」"
TC012,【办理场所要求】字段显示和空值处理验证,P1,"已登录系统;已进入政策详情页","1. 观察[户籍为县城的抵押权人是否可到市区上牌和抵押]、[办理地点具体名称]、[县城上牌的车辆是否可在市区抵押]字段的默认内容
2. 观察[办理机构是否可现场收费]字段的选项
3. 检查办理地点具体名称字段为空时的显示内容","1. 三个输入框字段显示对应的默认提示内容
2. 办理机构是否可现场收费显示是/否选项，可正常选择
3. 办理地点具体名称为空时显示「待调研」"
TC013,【批量抵押】字段显示和空值处理验证,P1,"已登录系统;已进入政策详情页","1. 观察[是否可批量抵押]字段的选项
2. 观察[一人一天最大办理次数]、[一人一次最多办理抵押单的数量]、[批量车当天可以出本的上限台数]字段的默认内容
3. 检查一人一天最大办理次数、一人一次最多办理抵押单的数量、批量车当天可以出本的上限台数字段为空时的显示内容","1. 是否可批量抵押显示是/否选项，可正常选择
2. 三个输入框字段显示对应的默认提示内容
3. 三个字段为空时均显示「待调研」"