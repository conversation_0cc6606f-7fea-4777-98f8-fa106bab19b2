用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【业务模块配置】业务类型和办理方式联动验证,P0,"已登录系统;已进入自定义下载页面","1. 在业务模块选择中选择[其他业务]，观察业务类型下拉框的变化
2. 在业务类型下拉框中观察是否包含[线上抵押]选项，选择线上抵押
3. 在[是否选择常用办理方式]字段选择[是]，观察办理方式下拉框的显示内容
4. 在[是否选择常用办理方式]字段选择[否]，观察办理方式下拉框的变化","1. 选择其他业务后业务类型下拉框正确显示相关选项
2. 业务类型下拉框首位显示线上抵押选项，选择正常
3. 选择是时办理方式包含车管所办理、云车服办理、邮局办理、警邮平台办理、服务站办理、政务中心办理六个选项
4. 选择否时办理方式下拉框显示其他相关选项"
TC002,【业务模块配置】必填字段验证,P1,"已登录系统;已进入自定义下载页面","1. 不选择业务类型，尝试进行下一步操作，观察系统提示
2. 不选择[是否选择常用办理方式]，尝试进行下一步操作，观察系统提示
3. 完整填写所有必填字段，进行下一步操作","1. 业务类型未选择时系统提示该字段为必选
2. 是否选择常用办理方式未选择时系统提示该字段为必选
3. 所有必填字段填写完整后可正常进入下一步"
TC003,【字段选择配置】基本信息和资料明细字段验证,P0,"已登录系统;已进入字段选择配置页面","1. 在基本信息模块中观察[特殊情况]字段的显示
2. 在资料明细模块中观察私户车线上抵押的字段选项
3. 在资料明细模块中观察公户车线上抵押的字段选项
4. 分别点击私户车和公户车的[下拉选择资料]和[全选]功能","1. 基本信息模块正确显示特殊情况字段选项
2. 私户车线上抵押显示下拉选择资料和全选两个选项
3. 公户车线上抵押显示下拉选择资料和全选两个选项
4. 下拉选择资料和全选功能正常工作"
TC004,【字段选择配置】抵押要求和收费情况字段验证,P1,"已登录系统;已进入字段选择配置页面","1. 在抵押要求模块中观察包含的字段列表
2. 在收费情况模块中观察包含的字段列表
3. 选择部分抵押要求字段，观察选择状态
4. 选择部分收费情况字段，观察选择状态","1. 抵押要求模块包含抵押人是否需要到场、有违章对抵押有无影响、脱审对抵押有无影响、合同上是否可以盖合同专用章、车辆抵押状态下是否可以办理营运证、营运车辆是否可以抵押、抵押资料填写要求、黄牌车/货车特殊情况备注等字段
2. 收费情况模块包含抵押工本费、抵押渠道费、抵押人不到场渠道费、牛工代办费、柜台非官方收费、提档费、买号费、归档期是否可以加急、加急归档费用等字段
3. 抵押要求字段选择状态正确显示和保存
4. 收费情况字段选择状态正确显示和保存"
TC005,【字段选择配置】抵押时效和办理场所字段验证,P1,"已登录系统;已进入字段选择配置页面","1. 在抵押时效模块中观察包含的字段列表
2. 在办理场所要求模块中观察包含的字段列表
3. 在批量抵押模块中观察包含的字段列表
4. 测试各模块字段的选择功能","1. 抵押时效模块包含新车上牌和抵押是否可以一起办理、新车上牌后多久可抵押、二手车过户和抵押是否可以一起办理、二手车过户后多久可抵押、收到资料齐全后多久可以出证、最长出证时间、办理回执类型、凭证样式、归档期是否可以加急、加急归档费用、抵押是否需要预约、抵押需要提前多久预约、约号方式、约号流程说明等字段
2. 办理场所要求模块包含县城上牌的车辆是否可在市区抵押、户籍为县城的抵押权人是否可到市区上牌和抵押、办理地点具体名称、办理机构是否可现场收费等字段
3. 批量抵押模块包含是否可批量抵押、一人一次最多办理抵押单的数量、一人一天最大办理次数、批量车当天内可以出本的上限台数等字段
4. 各模块字段选择功能正常，状态保存正确"
TC006,【操作功能】全选和操作按钮验证,P0,"已登录系统;已进入字段选择配置页面","1. 点击[选择全部字段]功能，观察所有字段的选中状态
2. 点击[取消]按钮，观察页面跳转情况
3. 选择部分字段后点击[下载]按钮，观察下载功能执行情况","1. 选择全部字段时所有可用字段均被选中
2. 点击取消按钮正常返回上一页面或关闭当前操作
3. 点击下载按钮正常触发文件下载功能"
TC007,【下载文件格式】基本信息和资料明细内容验证,P0,"已登录系统;已完成字段选择;已下载文件","1. 打开下载的文件，观察基本信息部分的字段内容
2. 观察资料明细部分的私户车线上抵押内容
3. 观察资料明细部分的公户车线上抵押内容
4. 检查文件结构的完整性","1. 基本信息包含序号、省份、城市、区县、业务类型、办理方式、是否为常用办理方式、特殊情况等字段
2. 私户车线上抵押和私户车线上抵押资料特殊要求说明字段正确显示
3. 公户车线上抵押和公户车线上抵押资料特殊要求说明字段正确显示
4. 文件结构完整，包含政策基本信息、资料明细和三级地区特殊处理"
TC008,【下载文件格式】抵押要求和收费情况内容验证,P1,"已登录系统;已完成字段选择;已下载文件","1. 在下载文件中观察抵押要求部分的字段内容
2. 在下载文件中观察收费情况部分的字段内容
3. 检查字段内容的完整性和准确性","1. 抵押要求部分包含抵押人是否需要到场、有违章对抵押有无影响、脱审对抵押有无影响、合同上是否可以盖合同专用章、车辆抵押状态下是否可以办理营运证、营运车辆是否可以抵押、抵押资料填写要求、黄牌车/货车特殊情况备注等字段
2. 收费情况部分包含抵押工本费、抵押渠道费、抵押人不到场渠道费、牛工代办费、柜台非官方收费、提档费、买号费、归档期是否可以加急、加急归档费用等字段
3. 所有字段内容完整准确，与选择的字段一致"
TC009,【下载文件格式】抵押时效和办理场所内容验证,P1,"已登录系统;已完成字段选择;已下载文件","1. 在下载文件中观察抵押时效部分的字段内容
2. 在下载文件中观察办理场所要求部分的字段内容
3. 在下载文件中观察批量抵押部分的字段内容","1. 抵押时效部分包含新车上牌和抵押是否可以一起办理、新车上牌后多久可抵押、二手车过户和抵押是否可以一起办理、二手车过户后多久可抵押、收到资料齐全后多久可以出证、最长出证时间、办理回执类型、凭证样式、归档期是否可以加急、加急归档费用、抵押是否需要预约、抵押需要提前多久预约、约号方式、约号流程说明等字段
2. 办理场所要求部分包含县城上牌的车辆是否可在市区抵押、户籍为县城的抵押权人是否可到市区上牌和抵押、办理地点具体名称、办理机构是否可现场收费等字段
3. 批量抵押部分包含是否可批量抵押、一人一次最多办理抵押单的数量、一人一天最大办理次数、批量车当天内可以出本的上限台数等字段"
TC010,【特殊处理规则】区县和业务类型显示规则验证,P1,"已登录系统;已下载包含多区县数据的文件","1. 观察关联区县数量大于1时的区县显示情况
2. 观察每种业务类型下[是否为常用办理方式]和[特殊情况]的显示
3. 检查资料显示规则的执行情况","1. 关联区县数量大于1时区县展示空白单元格，不展示具体区县信息
2. 每种业务类型下都正确展示是否为常用办理方式和特殊情况字段
3. 资料按特定顺序展示，同一资料类型下的文件展示在一个单元格中，包含序号、资料名称、份数、盖章类型信息"
TC011,【特殊处理规则】三级地区特殊处理验证,P2,"已登录系统;已下载包含特殊地区的文件","1. 观察湖北省仙桃市的显示情况，检查县区字段
2. 观察新疆省石河子市的显示情况，检查县区字段
3. 观察新疆省胡杨河市的显示情况","1. 湖北省仙桃市县区字段为空，符合特殊处理规则
2. 新疆省石河子市县区字段为空，符合特殊处理规则
3. 新疆省胡杨河市不展示，符合特殊处理规则"
TC012,【资料显示规则】全部资料选择和格式验证,P1,"已登录系统;已选择全部资料进行下载","1. 观察下载文件中资料的显示格式
2. 检查盖章类型的显示规则
3. 验证资料排序的正确性","1. 同一个资料类型下的文件展示在一个单元格中，格式正确
2. 盖章类型为空值或无需盖章时不显示盖章类型信息
3. 资料按特定顺序展示，序号、资料名称、份数、盖章类型信息完整"