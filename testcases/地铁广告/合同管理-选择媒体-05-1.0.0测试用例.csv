用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【查询功能】多字段查询验证,P0,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 已选择媒体并进入汇总明细列表页面","1. 在输入框中输入媒体类型关键词，观察明细表查询结果
2. 在输入框中输入投放档期关键词，观察明细表查询结果
3. 在输入框中输入所属线别关键词，观察明细表查询结果
4. 在输入框中输入所属站点关键词，观察明细表查询结果
5. 在输入框中输入等级关键词，观察明细表查询结果","1. 明细表正确筛选显示包含该媒体类型关键词的记录
2. 明细表正确筛选显示包含该投放档期关键词的记录
3. 明细表正确筛选显示包含该所属线别关键词的记录
4. 明细表正确筛选显示包含该所属站点关键词的记录
5. 明细表正确筛选显示包含该等级关键词的记录"
TC002,【汇总视图】默认展示和字段验证,P0,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 已选择媒体并进入汇总明细列表页面","1. 观察页面默认展示的视图类型
2. 观察汇总视图中的展示字段内容
3. 观察刊例合计字段的计算规则","1. 页面默认展示汇总视图信息
2. 汇总视图正确显示媒体类型、投放档期、数量、刊例合计（元）字段
3. 刊例合计字段显示所有媒体等级的刊例单价之和"
TC003,【汇总视图】单条删除功能验证,P0,"1. 已登录系统
2. 进入汇总视图页面
3. 汇总列表中有数据","1. 点击某条记录操作列的单条删除按钮，观察二次确认弹窗
2. 在确认弹窗中点击[取消]按钮，观察弹窗关闭情况
3. 再次点击单条删除按钮，在确认弹窗中点击[确认]按钮，观察删除结果和页面跳转","1. 二次确认弹窗正常打开，提示删除确认信息
2. 点击取消后弹窗关闭，数据不变，仍在汇总视图
3. 点击确认后删除成功，整个媒体类型下的媒体被删除，自动切换到明细tab，数据更新"
TC004,【汇总视图】批量删除功能验证,P1,"1. 已登录系统
2. 进入汇总视图页面
3. 汇总列表中有多条数据","1. 不选择任何数据，直接点击批量删除按钮，观察系统提示
2. 选择1条数据，点击批量删除按钮，观察删除结果
3. 选择2条以上数据，点击批量删除按钮，观察删除结果
4. 选择所有数据，点击批量删除按钮，观察删除结果","1. 删除失败，系统给出[请选择要删除的数据]提示
2. 删除成功，选中的1条数据被删除，切换到明细tab，数据更新
3. 删除成功，选中的多条数据被删除，切换到明细tab，数据更新
4. 删除成功，所有数据被删除，切换到明细tab，数据更新"
TC005,【汇总视图】数据展示规则验证,P1,"1. 已登录系统
2. 进入汇总视图页面
3. 已选择2个投放档期和2种媒体类型的媒体","1. 观察汇总视图中数据的展示规则和条数","1. 汇总视图显示4条记录：投放档期1-媒体类型1、投放档期2-媒体类型1、投放档期1-媒体类型2、投放档期2-媒体类型2"
TC006,【明细视图】视图切换和字段验证,P0,"1. 已登录系统
2. 进入汇总视图页面","1. 点击切换到明细视图，观察视图切换情况
2. 观察明细视图中的展示字段内容
3. 观察刊例单价字段的显示规则","1. 成功切换到明细视图，页面展示明细列表数据
2. 明细视图正确显示编号、媒体类型、投放档期、数量、刊例单价、所属线别、所属站点、等级、所在位置、出口字段
3. 刊例单价显示价格管理中当前媒体对应当前等级设置的发布费价格、适用站点、适用线别"
TC007,【明细视图】替换功能查询条件验证,P1,"1. 已登录系统
2. 进入明细视图页面
3. 明细列表中有数据","1. 点击某条记录的[替换]按钮，观察替换弹窗打开情况
2. 观察弹窗中查询条件的展示内容
3. 在编号查询框中输入关键词，观察模糊查询和精确查询功能
4. 观察媒体/组合媒体切换选择功能
5. 分别测试所属线别、所属站点、媒体类型、等级、所在位置查询条件","1. 替换弹窗正常打开，显示替换媒体的查询界面
2. 弹窗中显示编号、媒体/组合媒体、所属线别、所属站点、媒体类型、等级、所在位置查询条件
3. 编号查询支持模糊查询和精确查询，查询结果正确筛选
4. 默认选择媒体，可切换选择组合媒体，切换功能正常
5. 各查询条件均能正确筛选对应的媒体数据"
TC008,【明细视图】替换功能列表展示验证,P1,"1. 已登录系统
2. 已打开替换弹窗","1. 观察默认媒体模式下的列表展示内容
2. 切换到组合媒体模式，观察列表展示内容变化
3. 观察组合媒体模式下的过滤规则","1. 默认展示登录人（乙方）所在公司空闲的媒体资产包内包含的媒体信息，包含客户报备、媒体库存信息
2. 切换到组合媒体后，列表展示登录人所在公司空闲的媒体资产包内包含的组合媒体信息
3. 
1、已在当前列表的组合媒体不展示
2、意向中、已被锁位、已售出、不可用的组合媒体不展示
3、组合媒体里包含的组合销售可单独售卖媒体在所选档期内不可售卖时，不展示整个组合媒体"
TC009,【明细视图】替换和删除操作验证,P0,"1. 已登录系统
2. 进入明细视图页面
3. 明细列表中有数据","1. 在替换弹窗中选择新的媒体，点击[替换]按钮，观察替换结果
2. 点击某条记录的[删除]按钮，观察二次确认弹窗
3. 在确认弹窗中点击[确认]按钮，观察删除结果和页面跳转","1. 替换成功，当前媒体被新选择的媒体替换，弹窗关闭
2. 二次确认弹窗正常打开，提示删除确认信息
3. 删除成功，当前媒体被删除，自动切换到汇总tab，数据更新"
TC010,【明细视图】数据展示规则验证,P1,"1. 已登录系统
2. 进入明细视图页面
3. 已选择2个投放档期和2个媒体","1. 观察明细视图中数据的展示规则和条数","1. 明细视图显示4条记录：投放档期1-媒体1、投放档期2-媒体1、投放档期1-媒体2、投放档期2-媒体2"
TC011,【费用汇总】折前发布费总计计算验证,P0,"1. 已登录系统
2. 进入汇总明细列表页面
3. 明细表中有多条记录","1. 观察费用汇总区域的折前发布费总计显示
2. 手动计算明细表中（每一行刊例单价*数量）*行数，对比系统计算结果","1. 费用汇总区域正确显示折前发布费总计
2. 系统计算的折前发布费总计与手动计算结果一致，计算准确"
TC012,【费用汇总】以量制价折后价计算验证,P1,"1. 已登录系统
2. 进入汇总明细列表页面
3. 准备触发和未触发以量制价的数据","1. 观察触发以量制价时的折后价计算
2. 观察未触发以量制价时的折后价计算","1. 
1、触发以量制价时：显示该媒体类型对应等级设置的刊例单价*以量制价折扣*数量*购买时长
2、未触发以量制价时：显示该媒体类型对应的等级设置的刊例单价*数量*购买时长"
TC013,【广告位等级设置】数量设置验证,P0,"1. 已登录系统
2. 进入自动生成广告位方案页面","1. 观察广告位等级的展示内容
2. 观察每个等级可用数量的展示
3. 在S等级数量输入框输入0，观察输入结果
4. 在A等级数量输入框输入正整数且小于实际库存，观察输入结果
5. 在A++等级数量输入框输入超过实际库存的数量，观察输入限制","1. 广告位等级正确显示S、A、A++三个等级选项
2. 每个等级正确显示登录用户所在公司该等级广告位的可用数量
3. 可以输入0，输入成功
4. 可以输入小于实际库存的正整数，输入成功
5. 输入超过实际库存的数量时被限制或提示错误，不能超过实际库存"
TC014,【功能按钮】重置和自动生成验证,P1,"1. 已登录系统
2. 在广告位等级设置中已输入数量","1. 点击[重置]按钮，观察已输入数字的变化
2. 点击[自动生成广告位方案]按钮，观察自动方案弹窗打开情况","1. 重置按钮正常工作，已输入的数字被清空重置
2. 自动方案弹窗正常打开，显示自动生成的方案选择界面"
TC015,【自动方案弹窗】提示信息和倒计时验证,P1,"1. 已登录系统
2. 已打开自动方案弹窗","1. 观察弹窗中的提示信息内容
2. 观察倒计时的启动和计时功能
3. 等待倒计时结束，观察一键选择按钮状态变化","1. 弹窗正确显示提示信息：根据您设置的需求，系统自动为您生成以下可选方案，选择时间倒计时5分钟
2. 倒计时正常启动，从5分钟开始倒计时
3. 倒计时结束后所有一键选择按钮置灰不可点击"
TC016,【自动方案弹窗】期望等级和方案展示验证,P0,"1. 已登录系统
2. 已设置广告位等级（包含输入0和非0的等级）
3. 已打开自动方案弹窗","1. 观察期望广告位等级的显示内容
2. 观察三种自动生成方案的展示内容
3. 使用有历史投放的客户公司数据，观察方案1客户偏好的生成内容
4. 使用无历史投放的客户公司数据，观察方案1客户偏好的生成内容
5. 观察方案2行业优先的生成内容
6. 观察方案3客流优先的生成内容","1. 期望广告位等级显示媒体选择页面输入的等级，输入0的等级不展示
2. 弹窗正确显示方案1客户偏好、方案2行业优先、方案3客流优先三种方案
3. 方案1根据历史投放占比从高到低显示媒体类型和站点
4. 方案1随机生成媒体类型和站点
5. 方案2根据客户品牌所属行业历史投放的媒体类型、等级及站点从高到低显示
6. 方案3随机生成媒体类型、等级及站点"
TC017,【自动方案弹窗】操作功能验证,P1,"1. 已登录系统
2. 已打开自动方案弹窗
3. 弹窗中显示自动生成的方案","1. 点击任意方案中的站点名称，观察站点弹窗打开情况
2. 在站点弹窗中尝试操作，观察操作权限
3. 点击任意方案的[一键选择]按钮，观察选择结果","1. 点击站点名称正常打开选择站点弹窗
2. 站点弹窗只能查看不可操作，无法进行选择或修改操作
3. 一键选择成功，选择的方案数据正确展示到明细及汇总表中"
TC018,【页面操作】取消功能验证,P0,"1. 已登录系统
2. 在选择媒体页面已进行操作和数据修改","1. 点击页面[取消]按钮，观察提示弹窗打开情况
2. 在确认弹窗中点击[取消]按钮，观察弹窗关闭情况
3. 再次点击页面[取消]按钮，在确认弹窗中点击[确认]按钮，观察页面跳转和数据状态","1. 取消确认弹窗正常打开，提示[取消后所更改信息不会带入，确认取消吗]
2. 点击取消后弹窗关闭，停在当前页面，数据不发生变化
3. 点击确认后回到新增合同-投放信息页面，页面数据不发生变化" 