用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【页面展示】数据显示验证,P0,"1. 已登录系统
2. 已完成合同基础信息和投放信息填写
3. 进入合同复核页面","1. 观察页面展示的数据内容
2. 尝试编辑页面中的数据字段","1. 页面正确显示前两步填写的所有数据内容
2. 页面中的数据不可编辑，所有字段为只读状态"
TC002,【执行率计算】计算公式验证,P0,"1. 已登录系统
2. 系统中存在不同状态的合同和广告位明细数据
3. 进入合同复核页面","1. 观察系统执行率的计算结果
2. 手动计算执行率公式验证系统计算准确性","1. 系统正确显示当前执行率数值
2. 执行率=sum（执行中+已完成状态合同中广告位明细的投放数量）/sum（已失效/审批中/已撤回/已驳回/已签约/执行中/已完成/作废中/已作废/终止中/已终止状态合同中广告位明细中的投放数量），计算准确"
TC003,【规则分支】执行率>=80%规则3验证,P1,"1. 已登录系统
2. 当前公司执行率>=80%
3. 进入合同复核页面","1. 观察页面规则选择区域的显示情况
2. 观察系统自动应用的规则类型","1. 页面不展示规则选择选项
2. 系统自动按照规则3执行，无需用户选择"
TC004,【规则分支】商业广告公司规则1验证,P0,"1. 已登录系统
2. 当前公司类型=商业公司或广告公司
3. 当前公司执行率<80%
4. 进入合同复核页面","1. 观察页面规则显示和提示信息
2. 观察审批天数的计算和显示","1. 系统自动按照规则1执行，不显示规则选择
2. 页面显示提示：第一次预定/提交审批~审批通过共x天，您需在x天内完成合同审批，x天数来自营销规则中该公司对应分类的审批完成天数"
TC005,【规则分支】非商业广告公司规则选择验证,P0,"1. 已登录系统
2. 当前公司类型≠商业公司且≠广告公司
3. 当前公司执行率<80%
4. 进入合同复核页面","1. 观察页面规则选择选项的显示
2. 点击选择规则1，观察页面变化
3. 点击选择规则2，观察页面变化和付款相关字段显示","1. 页面同时展示规则1和规则2选项，可任选其一
2. 选择规则1后页面按规则1逻辑显示，提示审批天数信息
3. 选择规则2后页面显示付款提示信息和付款凭证上传区域"
TC006,【规则2付款凭证】上传功能验证,P1,"1. 已登录系统
2. 当前为非商业广告公司且执行率<80%
3. 已选择规则2
4. 进入合同复核页面","1. 观察付款金额的计算和显示
2. 尝试上传非图片格式文件，观察系统限制
3. 上传5张图片，观察上传结果
4. 尝试上传第6张图片，观察系统限制
5. 点击已上传图片进行预览，观察预览功能
6. 删除已上传的图片，观察删除功能","1. 付款金额正确显示为投放汇总-服务费合计，提示信息包含具体天数
2. 非图片格式文件上传失败，系统提示格式限制
3. 5张图片上传成功，显示上传成功状态
4. 第6张图片上传失败，系统提示不超过5张的限制
5. 图片预览功能正常，可查看大图
6. 图片删除功能正常，删除后界面更新"
TC007,【导出制式合同】显示条件验证,P1,"1. 已登录系统
2. 准备商业公司、广告公司和其他类型公司的账号","1. 使用商业公司账号登录，观察导出按钮显示
2. 使用广告公司账号登录，观察导出按钮显示
3. 使用其他类型公司账号登录，观察导出按钮显示","1. 商业公司账号登录时显示导出制式合同按钮
2. 广告公司账号登录时显示导出制式合同按钮
3. 其他类型公司账号登录时隐藏导出制式合同按钮"
TC008,【导出制式合同】下载功能验证,P1,"1. 已登录商业公司或广告公司账号
2. 进入合同复核页面
3. 页面显示导出按钮","1. 在合同名称不为空的情况下点击下载word，观察文件下载
2. 在合同名称为空的情况下点击下载word，观察文件下载
3. 打开下载的文件，检查文件内容","1. 下载成功，文件名称为合同名称.docx
2. 下载成功，文件名称为青岛地铁广告发布合同（品牌）.docx
3. 文件内容与模版一致，正确填充甲方乙方公司信息、联系人信息、发布媒体位置、形式、数量、期限等内容"
TC009,【上一步操作】未保存弹窗验证,P2,"1. 已登录系统
2. 进入合同复核页面
3. 页面中有数据修改","1. 点击[上一步]按钮，观察弹窗打开情况
2. 在编辑未保存弹窗中点击[取消]按钮，观察页面状态
3. 再次点击[上一步]按钮，在弹窗中点击[确定]按钮，观察页面跳转","1. 编辑未保存弹窗正常打开，提示用户确认操作
2. 点击取消后弹窗关闭，停在当前页面，数据不发生变化
3. 点击确定后页面关闭，数据不保存，返回上一页面"
TC010,【预订操作】商业合同广告位冲突验证,P0,"1. 已登录系统
2. 当前合同为商业合同
3. 选择的广告位投放档期存在非空闲状态的广告位","1. 点击[预订]按钮，观察系统冲突检测结果
2. 观察提示信息中的账号显示规则
3. 观察多个冲突档期的信息展示","1. 预订失败，停留在当前页面，页面上方显示冲突提示信息
2. 
1、占用公司为商业公司、广告公司时：显示用户名称（所属公司）
2、占用公司为其他类型时：显示所属公司名称
3. 有多个档期被不同公司占用时，依次展示每个占用信息，用分号间隔"
TC011,【预订操作】商业合同预订成功验证,P0,"1. 已登录系统
2. 当前合同为商业合同
3. 选择的广告位投放档期都为空闲状态","1. 点击[预订]按钮，观察预订结果和页面跳转
2. 观察合同状态变化
3. 观察保护机制启动和倒计时提示
4. 等待保护倒计时结束，观察超时处理","1. 预订成功，自动回到合同管理列表页面
2. 合同状态变更为预定中
3. 系统启动广告位保护倒计时，显示保护提示信息，保护时长=营销规则中该公司类型设置的预定保护小时数/24
4. 超过保护天数未提交审批时，当前合同内所有广告位及档期释放，其他合同可以选择"
TC012,【预订操作】储值合同余额验证,P1,"1. 已登录系统
2. 当前合同为储值合同
3. 选择的广告位都为空闲状态","1. 在账户余额>=0的情况下点击[预订]按钮，观察预订结果
2. 在账户余额<0的情况下点击[预订]按钮，观察系统处理","1. 余额>=0时预订成功，锁定规则与商业合同一致，合同状态变为预定中
2. 余额<0时保存失败，系统提示预定失败，余额不可小于0"
TC013,【提交审批】未预订直接审批验证,P0,"1. 已登录系统
2. 进入合同复核页面
3. 未点击过预订按钮","1. 直接点击[提交审批]按钮，观察锁位处理和审批启动
2. 观察保护倒计时的设置
3. 观察审批流程启动和页面跳转","1. 锁位规则与预定规则一致，广告位被锁定
2. 保护时间倒计时=合同广告位档期保护机制-该公司所属类型设置的预定保护小时数/24
3. 审批人收到审批消息通知，合同状态变为审批中，回到合同管理列表页面"
TC014,【提交审批】已预订后审批验证,P1,"1. 已登录系统
2. 已成功执行过预订操作
3. 在保护倒计时内进入合同复核页面","1. 点击[提交审批]按钮，观察保护倒计时的调整
2. 观察审批流程启动情况","1. 保护倒计时调整为（审批保护小时数-已经预订执行的保护小时数）/24
2. 其他审批规则与未点击过预订的情况一致，审批流程正常启动"
TC015,【规则2&3审批】审批通过验证,P0,"1. 已登录系统
2. 合同采用规则2或规则3
3. 合同状态为审批中","1. 执行审批通过操作，观察合同状态变化
2. 观察广告位明细的可用状态变化","1. 合同状态变更为已签约
2. 合同内所有投放档期内的广告位明细变为不可用状态"
TC016,【规则2&3审批】审批拒绝验证,P1,"1. 已登录系统
2. 合同采用规则2或规则3
3. 合同状态为审批中","1. 在当前倒计时<2天时执行第一次审批拒绝，观察倒计时处理
2. 在当前倒计时>=2天时执行第一次审批拒绝，观察倒计时处理
3. 执行第三次审批拒绝，观察合同状态和广告位释放","1. 当前倒计时<2天时，按照当前审批倒计时继续计时
2. 当前倒计时>=2天时，按照审批不通过的时间重新倒计时2天
3. 第三次拒绝后合同状态变为已失效，广告位及档期全部释放，若是规则3则服务费不退"
TC017,【规则2&3审批】审批超时处理验证,P1,"1. 已登录系统
2. 合同采用规则2或规则3
3. 审批未到通过状态且倒计时结束
4. 存在其他合同提交了重叠档期的广告位","1. 观察多个合同拥有相同档期广告位时的处理
2. 观察另一个合同已审核不通过三次的处理
3. 观察另一个合同审核不通过不超过三次的处理","1. 系统审核通过其中一个合同，另一个合同自动审核拒绝
2. 另一个合同已审核不通过三次时，合同状态设为失效
3. 另一个合同审核不通过不超过三次时，合同状态和保护机制按照提交和审批规则判断"
TC018,【规则1审批】审批通过验证,P0,"1. 已登录系统
2. 合同采用规则1
3. 合同状态为审批中","1. 执行审批通过操作，观察合同状态变化
2. 观察广告位明细的可用状态变化","1. 合同状态变更为已签约
2. 合同内所有投放档期内的广告位明细变为不可用状态"
TC019,【规则1审批】审批拒绝验证,P1,"1. 已登录系统
2. 合同采用规则1
3. 合同状态为审批中","1. 执行第一次审批拒绝，观察倒计时处理
2. 执行第三次审批拒绝，观察合同状态和广告位释放","1. 第一次拒绝后倒计时规则按照当前时间继续计时，不做修改
2. 第三次拒绝后合同状态变为已失效，广告位及档期全部释放"
TC020,【规则1审批】审批超时处理验证,P1,"1. 已登录系统
2. 合同采用规则1
3. 审批未到通过状态且倒计时结束
4. 存在其他合同提交了重叠档期的广告位","1. 观察多个合同拥有相同档期广告位时的处理
2. 观察另一个合同的审核不通过次数对状态的影响","1. 系统审核通过其中一个合同，另一个合同自动审核拒绝
2. 
1、另一个合同已审核不通过三次时：合同状态=失效
2、另一个合同审核不通过不超过三次时：合同状态和保护机制按照提交和审批来判断" 