用例编号,功能模块,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,合同管理-选择媒体,【选择媒体】页面展示和编号查询功能验证,P1,管理员已登录系统,"1. 进入选择媒体页面，观察页面整体布局和UI展示情况
2. 在编号查询框输入精确媒体编号""MT001""，观察查询结果
3. 清空查询框，输入部分编号""MT""进行模糊查询，观察查询结果","1. 页面布局与UI设计一致，各功能模块正常展示
2. 显示编号为""MT001""的精确匹配媒体信息
3. 显示所有包含""MT""的媒体信息列表"
TC002,合同管理-选择媒体,【选择媒体】数据来源规则全面验证,P1,"管理员已登录系统;系统中存在不同状态的媒体和组合媒体;存在自持属性不一致的媒体;存在合同冲突的媒体;存在小程序售出的媒体","1. 进入选择媒体页面，观察页面展示的媒体列表，检查是否只显示登录用户所在乙方公司适用资产包下的媒体和组合媒体
2. 查看媒体状态≠启用的媒体，观察这些媒体在列表中的展示情况
3. 将媒体A设置为组合不可单独售卖，且在其他组合媒体中，观察媒体A在选择媒体页面的展示情况
4. 将媒体A设置为组合不可单独售卖，且不在其他组合媒体中，观察媒体A在选择媒体页面的展示情况
5. 将媒体A设置为组合可单独售卖，观察媒体A在选择媒体页面的展示情况
6. 设置媒体自持属性与客户报备一致，设置另一媒体自持属性与客户报备不一致，观察两个媒体的展示情况
7. 将某媒体在其他合同中设置为预定中状态且投放档期被锁位，观察该媒体在当前合同选择页面的展示情况
8. 将某媒体在其他合同中设置为审批中、已驳回有倒计时、已签约、执行中、作废中状态，观察该媒体在当前合同选择页面的展示情况
9. 设置媒体为合同状态已失效xx天内、合同撤回冲突、客户报备意向点位未签订x天内，观察这些媒体的展示情况
10. 将某媒体设置为小程序售出的广告位包含状态，观察该媒体在选择媒体页面的展示情况","1. 仅显示当前乙方公司适用资产包下的媒体和组合媒体
2. 状态≠启用的媒体不在列表中展示
3. 组合不可单独售卖且在其他组合中的媒体A不单独展示
4. 组合不可单独售卖且不在其他组合中的媒体A单独展示
5. 组合可单独售卖的媒体A单独展示
6. 自持属性一致的媒体展示，不一致的媒体不展示
7. 预定中且被锁位的媒体在所选档期内不展示
8. 审批中、已驳回有倒计时、已签约、执行中、作废中状态的媒体不展示
9. 已失效xx天内、撤回冲突、报备意向未签订x天内的媒体不展示
10. 小程序售出包含的媒体不展示"
TC003,合同管理-选择媒体,【选择媒体】档期选择功能入口和模式切换验证,P0,管理员已登录系统,"1. 点击[选择档期]按钮，观察弹窗打开情况
2. 观察档期模式默认选中[完全自定义]状态
3. 点击切换至[固定周期]模式，观察界面变化
4. 点击切换至[自定义天数]模式，观察界面变化
5. 重新切换回[完全自定义]模式，观察界面恢复情况","1. 档期选择弹窗正常打开，显示档期模式、日期选择框、发布天数统计等字段
2. 档期模式默认选中完全自定义
3. 界面切换至固定周期模式，显示相应的周期选择选项
4. 界面切换至自定义天数模式，显示天数输入框
5. 界面恢复至完全自定义模式状态"
TC004,合同管理-选择媒体,【选择媒体】档期日期选择和发布天数计算验证,P0,管理员已登录系统,"1. 在完全自定义模式下，选择开始时间为2024-01-01，结束时间为2024-01-31，观察发布天数计算结果
2. 在固定周期模式下，观察默认展示一周7天，选择下拉选项[4周(28天)]，设置开始时间为2024-01-01，观察结束时间自动计算
3. 选择下拉选项[12周(84天)]、[半年(182天)]、[一年(365天)]，分别观察时间计算
4. 在自定义天数模式下，输入发布天数30，设置开始时间为2024-01-01，观察结束时间自动计算
5. 在自定义天数模式下，设置结束时间为2024-01-30，观察开始时间自动计算","1. 发布天数正确显示31天(结束时间-开始时间+1)
2. 默认显示一周7天，选择4周28天后，结束时间自动计算为2024-01-28
3. 12周、半年、一年选项的时间计算分别正确，选择开始/结束时间后互相自动计算
4. 输入30天后，结束时间自动计算为2024-01-30
5. 设置结束时间后，开始时间根据天数自动计算"
TC005,合同管理-选择媒体,【选择媒体】档期选择边界值和校验规则验证,P2,管理员已登录系统,"1. 在完全自定义模式下，设置开始时间2024-01-31晚于结束时间2024-01-01，观察系统处理
2. 设置开始时间等于结束时间2024-01-15，观察系统处理
3. 在自定义天数模式下，不输入天数保持为空，尝试设置时间，观察必填校验
4. 在自定义天数模式下，输入负数""-10""和非正整数""0""，观察系统处理
5. 在自定义天数模式下，输入超大值""10000""，观察最大值限制","1. 系统提示开始时间不能晚于结束时间，选择失败
2. 系统允许开始时间等于结束时间
3. 系统提示自定义天数为必填项
4. 系统不允许输入负数和非正整数，提示限制输入正整数
5. 系统提示最大输入9999，不允许输入10000"
TC006,合同管理-选择媒体,【选择媒体】档期选择删除功能验证,P1,管理员已登录系统,"1. 在档期选择弹窗中添加一个档期，观察删除按钮显示情况
2. 再添加第二个档期，观察删除按钮显示情况
3. 点击其中一个档期的删除按钮，观察删除操作和剩余档期情况
4. 再次添加档期使总数达到两个以上，观察删除按钮状态
5. 删除档期直到只剩一个，观察删除按钮状态变化","1. 只有一条档期数据时不展示删除按钮
2. 有两条档期数据时展示删除按钮
3. 点击删除按钮直接删除该档期，不需要确认
4. 两条以上档期时每条都展示删除按钮
5. 剩余一条档期时删除按钮不再展示"
TC007,合同管理-选择媒体,【选择媒体】档期选择取消按钮功能验证,P1,管理员已登录系统,"1. 在档期选择弹窗中添加和配置档期信息，不点击确认
2. 点击[取消]按钮，观察是否出现编辑未保存提示确认弹窗
3. 在确认弹窗中点击[取消]按钮，观察弹窗处理和数据状态
4. 重新点击[取消]按钮，在确认弹窗中点击[确定]按钮，观察跳转和数据处理","1. 档期信息配置完成但未确认保存
2. 出现编辑未保存提示确认弹窗
3. 提示弹窗关闭，留在档期选择弹窗，数据不发生变化
4. 提示弹窗关闭，回到选择媒体页面，没有档期被选中"
TC008,合同管理-选择媒体,【选择媒体】档期选择确认按钮和重叠检查验证,P0,管理员已登录系统,"1. 在档期选择弹窗中不填写任何档期信息，直接点击[确认]按钮，观察必填校验
2. 添加一个有效的档期信息，点击[确认]按钮，观察保存结果
3. 重新进入档期选择，添加第一个档期2024-01-01至2024-01-15，添加第二个档期2024-01-01至2024-01-20(开始时间相同)，点击[确认]按钮
4. 修改第二个档期为2024-01-15至2024-01-25(结束时间=开始时间)，点击[确认]按钮
5. 修改第二个档期为2024-01-05至2024-01-10(时间段包含在第一个内)，点击[确认]按钮
6. 修改第二个档期为2024-01-20至2024-01-30(无重叠)，点击[确认]按钮","1. 系统进行必填校验，提示填写档期信息
2. 档期保存成功，弹窗关闭，回到选择媒体页面，显示所选档期和计算的天数
3. 系统检测到开始时间相同，保存失败并给出重叠提示
4. 系统检测到结束时间=开始时间，保存失败并给出重叠提示
5. 系统检测到时间段包含关系，保存失败并给出重叠提示
6. 任意一段时间与其他时间没有重叠，保存成功，回到选择媒体页面，显示所选档期"
TC009,合同管理-选择媒体,【选择媒体】档期确认成功后媒体空闲时间验证,P1,"管理员已登录系统;已成功选择档期","1. 观察档期确认成功后选择媒体页面的档期展示
2. 验证页面依次展示选择的档期和计算的天数显示
3. 观察可选择的媒体列表，验证媒体空闲时间是否包含上刊施工天数和下刊缓冲天数
4. 尝试选择空闲时间不足以包含上刊施工天数和下刊缓冲天数的媒体，观察系统处理","1. 选择媒体页面正确显示已选择的档期信息
2. 档期按顺序展示，显示计算后的天数
3. 可选媒体的空闲时间包含所选档期内的上刊施工天数和下刊缓冲天数
4. 空闲时间不足的媒体不可选择或给出相应提示" 