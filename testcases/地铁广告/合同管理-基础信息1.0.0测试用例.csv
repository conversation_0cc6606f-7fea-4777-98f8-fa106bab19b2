用例编号,功能模块,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,合同管理,【基础信息】商业公司用户合同编号和合同名称字段验证,P0,商业公司用户已登录系统,"1. 进入合同管理新增页面
2. 观察合同编号字段显示状态
3. 在合同名称字段输入""测试合同""
4. 清空合同名称字段
5. 点击保存按钮
6. 提交审批流程
7. 在审批页面填写合同编号为""HT2024001""
8. 在审批页面填写合同名称为""审批合同""
9. 完成审批流程，查看合同详情页面","1. 合同编号字段不显示
2. 合同名称字段显示为""测试合同""
3. 合同名称字段显示为空
4. 保存成功，无错误提示
5. 提交审批成功
6. 进入审批流程
7. 合同编号显示为""HT2024001""
8. 合同名称显示为""审批合同""
9. 
1、审批完成
2、合同编号显示为""HT2024001""，合同名称显示为""审批合同"""
TC002,合同管理,【基础信息】非商业公司用户合同编号和合同名称字段验证,P0,非商业公司用户已登录系统,"1. 进入合同管理新增页面
2. 观察合同名称字段默认值
3. 清空合同名称字段
4. 点击保存按钮
5. 提交审批流程
6. 完成审批流程
7. 查看合同详情页面
8. 在合同名称字段输入""自定义合同名称""
9. 点击保存按钮，查看合同详情页面","1. 合同名称字段默认显示""青岛地铁广告发布合同""
2. 合同名称字段显示为空
3. 合同名称字段显示为空
4. 显示错误提示：合同名称不能为空
5. 提交审批成功
6. 审批完成
7. 合同编号自动生成，格式为：公司前四字首字母-年月-001
8. 合同名称字段显示为""自定义合同名称""
9. 
1、保存成功
2、合同名称显示为""自定义合同名称/客户品牌名称"""
TC003,合同管理,【基础信息】合同类型和发布类型字段验证,P1,用户已登录系统,"1. 进入合同管理新增页面
2. 观察合同类型字段默认值
3. 点击合同类型下拉框
4. 选择合同类型为[储值合同]
5. 观察储值金额字段显示状态
6. 在储值金额字段输入""1000.50""
7. 点击发布类型下拉框
8. 选择发布类型为[商业-本地]
9. 点击保存按钮，查看保存结果","1. 合同类型字段默认显示[商业合同]
2. 合同类型字段显示[商业合同]
3. 显示下拉选项：储值合同、商业合同、公益类、内部合同
4. 合同类型字段显示[储值合同]
5. 储值金额字段显示，必填标识
6. 储值金额字段显示""1000.50""
7. 显示下拉选项：商业-本地、商业-外埠、一类公益、二类公益、集团公益、广告商城-个人/小微、商业-内部
8. 发布类型字段显示[商业-本地]
9. 保存成功，无错误提示"
TC004,合同管理,【基础信息】储值金额字段边界值验证,P1,用户已登录系统,"1. 进入合同管理新增页面
2. 选择合同类型为[储值合同]
3. 在储值金额字段输入负数""-100""
4. 在储值金额字段输入""0""
5. 在储值金额字段输入""999999999.99""
6. 在储值金额字段输入""1000000000.00""
7. 在储值金额字段输入小数""100.123""
8. 在储值金额字段输入字母""abc""
9. 点击保存按钮，观察验证结果","1. 进入新增页面
2. 合同类型显示[储值合同]，储值金额字段显示
3. 显示错误提示：请输入正数
4. 显示错误提示：请输入正数
5. 储值金额字段显示""999999999.99""
6. 显示错误提示：超出最大限制
7. 储值金额字段显示""100.12""（保留两位小数）
8. 显示错误提示：请输入数字
9. 保存失败，显示相应错误提示"
TC005,合同管理,【基础信息】从客户报备进入新增合同的甲方信息验证,P1,"管理员已登录系统;存在已批准的客户报备记录","1. 从客户报备列表进入新增合同页面
2. 观察甲方公司名称字段
3. 尝试修改甲方公司名称
4. 观察品牌字段默认值
5. 修改品牌字段为""新品牌""
6. 观察联系人信息默认显示
7. 修改联系人姓名为""张三""
8. 修改联系电话为""13800138000""
9. 选择所在省市区为[山东省-青岛市-市南区]
10. 修改详细地址为""测试地址""
11. 修改职务为""经理""
12. 修改电子邮件为""<EMAIL>""
13. 点击保存按钮，查看保存结果","1. 甲方公司名称显示客户报备中的客户公司全称
2. 甲方公司名称字段显示客户公司全称
3. 甲方公司名称字段不可编辑
4. 品牌字段显示客户报备中的品牌
5. 品牌字段显示""新品牌""
6. 显示一行联系人信息数据
7. 联系人姓名字段显示""张三""
8. 联系电话字段显示""13800138000""
9. 所在省市区显示[山东省-青岛市-市南区]
10. 详细地址字段显示""测试地址""
11. 职务字段显示""经理""
12. 电子邮件字段显示""<EMAIL>""
13. 保存成功"
TC006,合同管理,【基础信息】从合同列表进入新增合同的甲方信息验证,P1,管理员已登录系统,"1. 从合同列表点击新增按钮
2. 观察甲方公司名称字段
3. 点击甲方公司名称下拉框
4. 选择甲方公司名称为[测试公司A]
5. 观察品牌字段显示状态
6. 选择品牌为[品牌A]
7. 选择品牌为[品牌B]
8. 点击全选按钮
9. 观察联系人信息显示
10. 点击新增联系人按钮
11. 填写新增联系人信息
12. 点击删除按钮
13. 在确认弹窗点击确定，观察删除结果","1. 甲方公司名称字段显示[请选择]
2. 甲方公司名称字段显示[请选择]
3. 显示下拉选项：当前用户负责的已批准&保护中状态的客户公司
4. 甲方公司名称显示[测试公司A]
5. 品牌字段显示对应的品牌选项
6. 品牌字段显示[品牌A]
7. 品牌字段显示[品牌A,品牌B]
8. 品牌字段显示所有品牌
9. 显示一行联系人信息，删除按钮置灰
10. 新增一行联系人信息
11. 新增联系人信息填写完成
12. 弹出二次确认弹窗
13. 
1、弹窗关闭
2、选中的联系人信息被删除"
TC007,合同管理,【基础信息】联系人信息字段验证,P1,管理员已登录系统,"1. 进入合同管理新增页面
2. 选择甲方公司名称
3. 在联系人姓名字段输入1个字符
4. 在联系人姓名字段输入50个字符
5. 在联系人姓名字段输入51个字符
6. 在联系电话字段输入""13800138000""
7. 在联系电话字段输入字母""abc""
8. 在详细地址字段输入1个字符
9. 在详细地址字段输入50个字符
10. 在详细地址字段输入51个字符
11. 在职务字段输入""经理""
12. 在职务字段输入51个字符
13. 在电子邮件字段输入""<EMAIL>""
14. 在电子邮件字段输入无效邮箱""invalid-email""
15. 点击保存按钮，观察验证结果","1. 进入新增页面
2. 甲方公司名称选择完成
3. 联系人姓名字段显示1个字符
4. 联系人姓名字段显示50个字符
5. 显示错误提示：超出字符限制
6. 联系电话字段显示""13800138000""
7. 显示错误提示：请输入有效电话号码
8. 详细地址字段显示1个字符
9. 详细地址字段显示50个字符
10. 显示错误提示：超出字符限制
11. 职务字段显示""经理""
12. 显示错误提示：超出字符限制
13. 电子邮件字段显示""<EMAIL>""
14. 显示错误提示：请输入有效邮箱地址
15. 保存失败，显示相应错误提示"
TC008,合同管理,【基础信息】乙方信息字段验证,P1,用户已登录系统,"1. 进入合同管理新增页面
2. 观察乙方公司名称字段默认值
3. 点击乙方公司名称下拉框
4. 选择乙方公司名称
5. 观察提报人字段
6. 在联系电话字段输入""13800138000""
7. 在电子邮件字段输入""<EMAIL>""
8. 选择所在省市区为[山东省-青岛市-市南区]
9. 在详细地址字段输入""测试地址""
10. 观察创建日期字段
11. 点击保存按钮，查看保存结果
12. 编辑保存后再次查看创建日期","1. 乙方公司名称显示默认公司名称
2. 乙方公司名称字段显示当前用户所在公司
3. 显示下拉选项：商业公司、广告公司
4. 乙方公司名称字段显示选择的公司
5. 提报人字段显示当前登录人姓名，不可编辑
6. 联系电话字段显示""13800138000""
7. 电子邮件字段显示""<EMAIL>""
8. 所在省市区显示[山东省-青岛市-市南区]
9. 详细地址字段显示""测试地址""
10. 创建日期字段显示当天年月日
11. 保存成功
12. 创建日期更新为编辑保存的日期"
TC009,合同管理,【基础信息】页面按钮操作验证,P1,用户已登录系统,"1. 进入合同管理新增页面
2. 填写部分基础信息
3. 点击取消按钮
4. 在弹窗中点击取消
5. 再次点击取消按钮
6. 在弹窗中点击确定
7. 重新进入新增页面
8. 填写完整基础信息
9. 点击下一步按钮，观察页面跳转结果","1. 进入新增页面
2. 基础信息部分填写完成
3. 弹出编辑未保存弹窗
4. 弹窗关闭，停留在当前页面
5. 再次弹出编辑未保存弹窗
6. 页面关闭，数据不保存
7. 进入新增页面
8. 基础信息填写完整
9. 跳转到投放信息录入页面"
TC010,合同管理,【基础信息】权限验证和异常场景测试,P2,用户已登录系统,"1. 连续快速点击保存按钮
2. 连续快速点击下一步按钮
3. 在网络延迟情况下点击保存
4. 清空所有必填字段
5. 点击保存按钮
6. 在合同名称字段输入特殊字符""!@#$%^&*()""
7. 在储值金额字段输入科学计数法""1e6""
8. 在联系电话字段输入特殊格式""+86-138-0013-8000""
9. 在电子邮件字段输入包含特殊字符的邮箱""<EMAIL>""
10. 点击保存按钮，观察验证结果","1. 系统正常响应，无重复提交
2. 系统正常响应，无重复跳转
3. 显示加载状态，超过1秒显示加载提示
4. 所有必填字段显示为空
5. 显示错误提示：请填写必填字段
6. 合同名称字段显示特殊字符
7. 显示错误提示：请输入有效数字
8. 联系电话字段显示""+86-138-0013-8000""
9. 电子邮件字段显示""<EMAIL>""
10. 保存成功，特殊字符正常处理" 