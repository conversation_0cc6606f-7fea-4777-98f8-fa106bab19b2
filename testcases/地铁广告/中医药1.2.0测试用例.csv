用例编号,功能模块,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,网址管理,【网址管理】网站标题和logo配置功能验证,P0,管理员已登录系统,"1.进入网址管理模块
2.点击[网站标题]输入框
3.输入网站标题""中医药学习平台""
4.点击[logo上传]按钮
5.选择本地图片文件
6.点击[保存]按钮","1.成功进入网址管理模块
2.网站标题输入框获得焦点
3.网站标题显示为""中医药学习平台""
4.弹出文件选择对话框
5.成功选择图片文件
6.显示[保存成功]提示信息"
TC002,网址管理,【网址管理】轮播图添加课程跳转功能验证,P0,"管理员已登录系统;已配置网站标题和logo","1.点击[添加轮播图]按钮
2.选择[课程跳转]类型
3.从课程列表中选择""中医基础理论""课程
4.上传轮播图图片文件
5.设置排序值为1
6.点击[保存]按钮
7.在首页查看轮播图展示","1.弹出轮播图配置对话框
2.轮播图类型显示为课程跳转
3.课程选择框中显示""中医基础理论""
4.图片上传成功，显示预览
5.排序值设置为1
6.显示[保存成功]提示信息
7.首页轮播图显示该课程图片，点击跳转到对应课程页面"
TC003,网址管理,【网址管理】轮播图添加外部链接功能验证,P1,"管理员已登录系统;已配置网站标题和logo","1.点击[添加轮播图]按钮
2.选择[外部链接]类型
3.输入外部链接地址""https://www.example.com""
4.上传轮播图图片文件
5.设置排序值为2
6.点击[保存]按钮
7.在首页查看轮播图展示","1.弹出轮播图配置对话框
2.轮播图类型显示为外部链接
3.外部链接输入框显示""https://www.example.com""
4.图片上传成功，显示预览
5.排序值设置为2
6.显示[保存成功]提示信息
7.首页轮播图显示该图片，点击在新窗口打开外部链接"
TC004,网址管理,【网址管理】轮播图云端图片展示功能验证,P1,"管理员已登录系统;已配置网站标题和logo","1.点击[添加轮播图]按钮
2.选择[云端图片]类型
3.从云端图片库中选择图片
4.设置排序值为3
5.点击[保存]按钮
6.在首页查看轮播图展示","1.弹出轮播图配置对话框
2.轮播图类型显示为云端图片
3.成功选择云端图片，显示预览
4.排序值设置为3
5.显示[保存成功]提示信息
6.首页轮播图显示选中的云端图片"
TC005,网址管理,【网址管理】轮播图视频播放功能验证,P1,"管理员已登录系统;已配置网站标题和logo","1.点击[添加轮播图]按钮
2.选择[视频播放]类型
3.上传视频文件
4.设置排序值为4
5.点击[保存]按钮
6.在首页查看轮播图展示","1.弹出轮播图配置对话框
2.轮播图类型显示为视频播放
3.视频上传成功，显示视频预览
4.排序值设置为4
5.显示[保存成功]提示信息
6.首页轮播图显示视频播放器，支持播放控制"
TC006,网址管理,【网址管理】轮播图排序顺序展示验证,P0,"管理员已登录系统;已配置10张轮播图","1.配置10张轮播图，排序值分别为1-10
2.进入首页查看轮播图展示
3.观察轮播图切换顺序
4.点击轮播图切换按钮
5.验证轮播图展示顺序","1.成功配置10张轮播图
2.首页轮播图区域正常显示
3.轮播图按照排序值1-10的顺序依次展示
4.轮播图切换按钮正常工作
5.轮播图展示顺序与配置的排序值完全一致"
TC007,网址管理,【网址管理】轮播图数量限制验证,P1,"管理员已登录系统;已配置9张轮播图","1.尝试添加第10张轮播图
2.配置轮播图信息并保存
3.尝试添加第11张轮播图
4.观察系统提示信息","1.成功添加第10张轮播图
2.第10张轮播图保存成功
3.系统显示[轮播图数量已达上限]提示信息
4.无法继续添加更多轮播图"
TC008,网址管理,【网址管理】配置轮播图后下架课程功能验证,P1,"管理员已登录系统;已配置包含课程跳转的轮播图","1.进入课程管理模块
2.找到轮播图中引用的课程
3.点击[下架]按钮
4.确认下架操作
5.返回首页查看轮播图状态","1.成功进入课程管理模块
2.找到对应课程并显示其状态
3.弹出下架确认对话框
4.课程状态变更为[已下架]
5.首页轮播图中该课程图片显示[课程已下架]提示，点击无响应"
TC009,网址管理,【网址管理】版权信息链接配置验证,P1,管理员已登录系统,"1.进入网址管理模块
2.找到[版权信息]配置区域
3.输入版权信息文本""© 2024 中医药学习平台""
4.输入版权链接地址""https://www.example.com/copyright""
5.点击[保存]按钮
6.在网站底部查看版权信息","1.成功进入网址管理模块
2.显示版权信息配置区域
3.版权信息文本显示为""© 2024 中医药学习平台""
4.版权链接地址显示为""https://www.example.com/copyright""
5.显示[保存成功]提示信息
6.网站底部显示版权信息，点击链接可跳转"
TC010,网址管理,【网址管理】轮播图防抖机制验证,P2,"管理员已登录系统;已配置轮播图","1.快速连续点击[添加轮播图]按钮5次
2.观察系统响应
3.等待1秒后再次点击按钮
4.验证操作结果","1.系统只响应第一次点击，弹出轮播图配置对话框
2.后续4次点击被忽略，无重复弹窗
3.1秒后按钮恢复正常响应状态
4.轮播图配置功能正常工作"
TC011,网址管理,【网址管理】网络异常时轮播图配置验证,P2,"管理员已登录系统;网络连接正常","1.断开网络连接
2.尝试添加轮播图
3.填写轮播图信息
4.点击[保存]按钮
5.恢复网络连接
6.重新尝试保存","1.网络断开后系统显示[网络连接异常]提示
2.轮播图配置界面正常显示
3.可以正常填写轮播图信息
4.点击保存时显示[网络异常，请稍后重试]提示
5.网络恢复后系统自动重连
6.轮播图保存成功，显示[保存成功]提示"
TC012,网址管理,【网址管理】轮播图并发操作验证,P2,"管理员已登录系统;已配置5张轮播图","1.同时打开两个浏览器窗口
2.在窗口A中修改轮播图排序
3.在窗口B中删除轮播图
4.在两个窗口中同时点击保存
5.刷新页面查看最终状态","1.两个浏览器窗口都能正常访问系统
2.窗口A中轮播图排序修改成功
3.窗口B中轮播图删除成功
4.系统正确处理并发操作，无数据冲突
5.页面刷新后显示正确的轮播图配置状态"
TC013,网址管理,【网址管理】轮播图边界值测试,P2,管理员已登录系统,"1.尝试上传0字节的图片文件
2.尝试上传超过10MB的图片文件
3.尝试输入空标题
4.尝试输入超长标题（超过100字符）
5.尝试设置排序值为0
6.尝试设置排序值为999","1.系统显示[文件大小不能为0]错误提示
2.系统显示[文件大小不能超过10MB]错误提示
3.系统显示[标题不能为空]错误提示
4.系统显示[标题长度不能超过100字符]错误提示
5.系统显示[排序值必须大于0]错误提示
6.系统显示[排序值不能超过999]错误提示"
TC014,网址管理,【网址管理】轮播图特殊字符处理验证,P2,管理员已登录系统,"1.在轮播图标题中输入特殊字符：!@#$%^&*()
2.在外部链接中输入包含特殊字符的URL
3.在版权信息中输入HTML标签：<script>alert('test')</script>
4.保存配置并查看显示效果","1.系统正常保存包含特殊字符的标题
2.系统正常保存包含特殊字符的URL
3.系统对HTML标签进行转义处理，显示为纯文本
4.页面正常显示，无脚本执行，无格式错误"
TC015,网址管理,【网址管理】轮播图API响应时间验证,P2,"管理员已登录系统;网络连接正常","1.点击[添加轮播图]按钮
2.填写轮播图信息
3.点击[保存]按钮
4.观察加载状态显示
5.等待操作完成","1.轮播图配置对话框正常弹出
2.轮播图信息填写界面正常显示
3.点击保存后立即显示[加载中]状态
4.加载状态持续显示直到操作完成
5.操作完成后显示[保存成功]提示，加载状态消失" 