用例编号,功能模块,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,合同管理,【列表页】页面基本结构和Tab切换验证,P0,管理员已登录系统,"1. 点击[合同执行]菜单，观察页面标题和布局
2. 检查tab区域显示，点击[待处理]tab，观察tab状态变化和数据变化
3. 点击[全部]tab，观察tab状态变化和数据变化","1. 成功进入合同管理列表页，页面标题正确显示，布局完整
2. tab区域显示[全部]和[待处理]两个选项，[全部]默认选中，[待处理]tab变为选中状态，列表数据更新为需要当前登录人员处理的合同数据
3. [全部]tab变为选中状态，[待处理]tab变为未选中状态，列表数据更新为全部合同数据"
TC002,合同管理,【列表功能】列表字段完整性和排序验证,P0,管理员已登录系统并进入合同管理页面,"1. 观察列表表头字段，检查每个字段的显示名称和数量
2. 观察列表数据排序规则，验证状态优先级排序和时间倒序排序","1. 列表表头显示15个字段：合同编号、合同名称、甲方公司名称、品牌、合同总额（元）、状态、保护倒计时、释放时间、合同发布时间、合同类型、余额（元）、发布类型、提报公司、提报人、创建日期，字段数量为15个，无遗漏
2. 列表数据按照排序规则正确排序，状态优先级排序：预定中>审批中>作废中>终止中，其他状态按照创建日期倒序排列"
TC003,合同管理,【查询功能】文本搜索框综合验证,P1,管理员已登录系统并进入合同管理页面,"1. 在文本搜索框输入准确的合同编号""HT001""，点击查询按钮，观察查询结果
2. 清空搜索框，输入合同名称部分内容""测试""，点击查询按钮，观察查询结果
3. 清空搜索框，输入甲方公司名称部分内容""ABC""，点击查询按钮，观察查询结果
4. 清空搜索框，输入品牌部分内容""品牌A""，点击查询按钮，观察查询结果
5. 清空搜索框，输入提报公司部分内容""青铁""，点击查询按钮，观察查询结果
6. 清空搜索框，输入提报人姓名""张三""，点击查询按钮，观察查询结果","1. 搜索框显示输入的合同编号，列表显示合同编号为""HT001""的合同记录
2. 搜索框显示输入的部分内容，列表显示所有合同名称包含""测试""的合同记录
3. 搜索框显示输入的公司名称，列表显示所有甲方公司名称包含""ABC""的合同记录
4. 搜索框显示输入的品牌内容，列表显示所有品牌包含""品牌A""的合同记录
5. 搜索框显示输入的公司内容，列表显示所有提报公司包含""青铁""的合同记录
6. 搜索框显示提报人姓名，列表显示提报人为""张三""的合同记录"
TC004,合同管理,【查询功能】筛选条件综合验证,P1,管理员已登录系统并进入合同管理页面,"1. 点击状态下拉框，观察下拉选项内容，选择[预定中]状态，观察列表数据变化
2. 点击发布类型下拉框，观察下拉选项内容，选择[商业-本地]类型，观察列表数据变化
3. 点击合同类型下拉框，观察下拉选项内容，选择[储值合同]类型，观察列表数据变化
4. 点击合同发布时间筛选框，选择2024-01-01至2024-01-31，观察列表数据变化
5. 点击创建日期筛选框，选择2024-02-01至2024-02-28，观察列表数据变化","1. 状态下拉框显示12个选项：预定中、已失效、审批中、已撤回、已驳回、已签约、执行中、已完结、作废中、已作废、终止中、已终止，列表仅显示状态为[预定中]的合同记录
2. 发布类型下拉框显示7个选项：商业-本地、商业-外埠、一类公益、二类公益、集团公益、广告商城-个人/小微、商业-内部，列表仅显示发布类型为[商业-本地]的合同记录
3. 合同类型下拉框显示4个选项：储值合同、商业合同、公益类、内部合同，列表仅显示合同类型为[储值合同]的合同记录
4. 合同发布时间显示选择的时间段，列表显示合同发布时间在2024-01-01至2024-01-31之间的合同记录
5. 创建日期显示选择的时间段，列表显示创建日期在2024-02-01至2024-02-28之间的合同记录"
TC005,合同管理,【查询功能】多条件组合查询和重置验证,P1,管理员已登录系统并进入合同管理页面,"1. 在文本搜索框输入""测试""，选择状态为[预定中]，选择发布类型为[商业-本地]，选择合同类型为[商业合同]，设置合同发布时间为2024-01-01至2024-01-31，点击查询按钮，观察列表数据
2. 点击[重置]按钮，观察查询条件区域变化，观察列表数据变化","1. 文本搜索框显示""测试""，状态下拉显示[预定中]，发布类型下拉显示[商业-本地]，合同类型下拉显示[商业合同]，合同发布时间显示2024-01-01至2024-01-31，系统执行多条件查询，列表显示同时满足所有筛选条件的合同记录
2. 重置按钮被点击，所有查询条件恢复为默认状态：文本搜索框清空，下拉选择项清空，时间筛选清空，列表数据恢复为全部合同数据"
TC006,合同管理,【查询功能】时间筛选边界场景验证,P2,管理员已登录系统并进入合同管理页面,"1. 点击合同发布时间筛选框，只选择开始时间2024-01-01，点击确定，观察查询结果
2. 重置筛选条件，只选择结束时间2024-01-31，点击确定，观察查询结果
3. 重置筛选条件，选择开始时间=结束时间=2024-01-15，点击确定，观察查询结果
4. 重置筛选条件，选择开始时间为2024-02-01，结束时间为2024-01-01，点击确定，观察系统提示信息","1. 开始时间显示为2024-01-01，结束时间为空，列表显示合同发布时间大于等于2024-01-01的合同记录
2. 开始时间为空，结束时间显示为2024-01-31，列表显示合同发布时间小于等于2024-01-31的合同记录
3. 开始时间和结束时间均显示为2024-01-15，列表显示合同发布时间为2024-01-15的合同记录
4. 开始时间显示为2024-02-01，结束时间显示为2024-01-01，显示错误提示信息，提示开始时间不能大于结束时间"
TC007,合同管理,【列表功能】保护倒计时字段显示规则验证,P1,管理员已登录系统并进入合同管理页面,"1. 筛选状态为[预定中]的合同，观察列表中保护倒计时字段显示，找到剩余时间大于60分钟的合同记录，找到剩余时间在1-60分钟之间的合同记录，找到剩余时间小于1分钟的合同记录
2. 筛选状态为[已撤回]的合同，观察保护倒计时字段显示
3. 筛选状态为[已签约]的合同，观察保护倒计时字段显示","1. 列表显示预定中状态的合同，保护倒计时字段显示为实时倒计时，剩余时间大于60分钟的记录显示为Xh格式（如2h、5h），剩余时间在1-60分钟之间的记录显示为Xmin格式（如30min、59min），剩余时间小于1分钟的记录不显示倒计时
2. 列表显示已撤回状态的合同，保护倒计时字段显示剩余保护时间
3. 列表显示已签约状态的合同，保护倒计时字段不显示任何内容或显示为空"
TC008,合同管理,【列表功能】释放时间字段显示规则验证,P1,管理员已登录系统并进入合同管理页面,"1. 筛选状态为[已失效]的合同，观察释放时间字段显示，找到倒计时未结束的已失效合同，找到倒计时已结束的已失效合同
2. 筛选状态为[已签约]的合同，观察释放时间字段显示
3. 筛选状态为[执行中]的合同，观察释放时间字段显示","1. 列表显示已失效状态的合同，释放时间字段正常显示，倒计时未结束的已失效合同显示倒计时结束为0的时间年月日时分，倒计时已结束的已失效合同显示""--""
2. 列表显示已签约状态的合同，释放时间字段不显示任何内容或显示为空
3. 列表显示执行中状态的合同，释放时间字段不显示任何内容或显示为空"
TC009,合同管理,【权限功能】数据权限综合验证,P1,不同权限用户已登录系统,"1. 使用超级管理员用户登录，进入合同管理页面，观察列表数据范围
2. 使用无审批权限用户登录，进入合同管理页面，观察列表数据范围
3. 使用青铁商业公司有审批权限用户登录，进入合同管理页面，观察列表数据范围
4. 使用其他公司有审批权限用户登录，进入合同管理页面，观察列表数据范围","1. 超级管理员用户登录成功，成功进入合同管理页面，列表显示全部合同数据
2. 无审批权限用户登录成功，成功进入合同管理页面，列表仅显示该用户提报的合同数据
3. 青铁商业公司有审批权限用户登录成功，成功进入合同管理页面，列表显示全部合同数据
4. 其他公司有审批权限用户登录成功，成功进入合同管理页面，列表仅显示该用户所属公司的全部合同数据"
TC010,合同管理,【异常处理】防抖机制和网络异常验证,P2,管理员已登录系统并进入合同管理页面,"1. 在文本搜索框输入""测试""，连续快速点击查询按钮5次，观察查询执行次数
2. 模拟网络中断情况，点击查询按钮，观察系统响应，恢复网络连接，再次点击查询按钮，观察查询结果","1. 文本搜索框显示""测试""，查询按钮被连续快速点击，查询操作只执行一次，防抖机制生效
2. 网络连接中断，查询按钮被点击，系统显示网络错误提示或加载状态，网络连接恢复，查询按钮再次被点击，查询操作正常执行，显示查询结果" 