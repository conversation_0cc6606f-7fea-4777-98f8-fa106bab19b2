用例编号,功能模块,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,合同管理-投放信息,【投放信息】页面基础展示验证,P0,管理员已登录系统,"1. 进入合同管理-投放信息页面，观察页面展示内容
2. 观察默认展示的tab页面
3. 点击[投放汇总]tab，观察汇总信息展示
4. 点击[投放明细]tab，观察明细信息展示
5. 点击[新增]按钮，观察页面跳转","1. 页面正常加载，显示投放汇总和投放明细两个tab
2. 默认展示[投放汇总]tab
3. 投放汇总tab显示媒体类型、级别、投放数量、发布费、服务费、签约总价等信息
4. 投放明细tab显示编号、媒体类型、级别、线路、站点、刊例单价、投放数量等字段
5. 成功跳转到选择媒体页面"
TC002,合同管理-投放信息,【投放汇总】媒体展示规则验证,P1,"管理员已登录系统;已配置多个相同投放档期和媒体类型的点位信息","1. 在投放汇总页面，观察同一投放档期&媒体类型的合并展示
2. 观察同一投放档期&媒体类型下相同级别（等级）的去重展示
3. 观察每个等级的投放数量合并展示","1. 同一投放档期&媒体类型的数据合并为一行显示
2. 相同级别（等级）的数据去重后合并显示
3. 每个等级的投放数量正确合并计算"
TC003,合同管理-投放信息,【发布费】刊例单价字段验证,P1,"管理员已登录系统;已配置价格管理信息","1. 观察刊例单价字段显示内容
2. 验证刊例单价取值来源
3. 观察适用线别/站点信息显示","1. 刊例单价字段正确显示对应媒体类型和级别的价格
2. 刊例单价取值为价格管理中当前媒体类型对应级别设置的刊例单价
3. 适用线别/站点信息正确显示"
TC004,合同管理-投放信息,【发布费】刊例单价折扣字段验证,P1,"管理员已登录系统;已配置价格管理信息","1. 观察刊例单价折扣字段的必填状态
2. 在刊例单价折扣字段输入正整数""8""，观察输入结果
3. 在刊例单价折扣字段输入负数""-5""，观察输入限制
4. 在刊例单价折扣字段输入小数""8.5""，观察输入限制
5. 输入折扣后观察全部折后总价的计算","1. 刊例单价折扣字段为非必填状态
2. 正整数""8""输入成功
3. 负数""-5""输入被拒绝，显示输入限制提示
4. 小数""8.5""输入被拒绝，显示正整数限制提示
5. 输入折扣后全部折后总价自动重新计算"
TC005,合同管理-投放信息,【发布费】刊例单价折扣合同类型验证,P1,"管理员已登录系统;已配置价格管理信息","1. 选择合同类型为公益合同，在刊例单价折扣字段输入低于该媒体类型该等级最低折扣的值，观察验证结果
2. 选择合同类型为非公益合同，在刊例单价折扣字段输入低于该等级发布费最低折扣的值，观察验证结果
3. 选择合同类型为非公益合同，在刊例单价折扣字段输入大于等于该等级发布费最低折扣的值，观察验证结果","1. 公益合同可输入低于该媒体类型该等级的最低折扣值，输入成功
2. 非公益合同拒绝输入低于最低折扣的值，显示验证提示
3. 非公益合同可输入大于等于最低折扣的值，输入成功"
TC006,合同管理-投放信息,【发布费】以量制价折扣验证,P1,"管理员已登录系统;已配置价格管理信息","1. 设置投放数量达到以量制价数量要求，观察以量制价折扣字段显示
2. 设置投放数量未达到以量制价数量要求，观察以量制价折扣字段显示
3. 验证以量制价折扣字段是否可修改","1. 投放数量达到以量制价数量时，显示价格管理中设置的以量制价折扣值
2. 投放数量未达到以量制价数量时，显示""--""
3. 以量制价折扣字段不可修改"
TC007,合同管理-投放信息,【发布费】全部折扣后总价字段验证,P1,"管理员已登录系统;已配置价格管理信息","1. 观察全部折扣后总价字段的必填状态
2. 在全部折扣后总价字段输入正整数""1000""，观察输入结果
3. 在全部折扣后总价字段输入负数""-500""，观察输入限制
4. 在全部折扣后总价字段输入小数""1000.5""，观察输入限制
5. 在全部折扣后总价字段输入低于最低折扣总价的值，观察验证提示
6. 输入全部折扣后总价后观察刊例单价的重新计算","1. 全部折扣后总价字段为必填状态
2. 正整数""1000""输入成功
3. 负数""-500""输入被拒绝，显示输入限制提示
4. 小数""1000.5""输入被拒绝，显示正整数限制提示
5. 输入低于最低折扣总价的值时显示验证提示
6. 输入后刊例单价根据公式重新计算"
TC008,合同管理-投放信息,【发布费】全部折扣后总价计算公式验证,P1,"管理员已登录系统;已配置价格管理信息","1. 设置刊例单价、时长、以量制价折扣、折扣、投放数量等参数
2. 观察全部折后总价的自动计算结果
3. 验证计算公式：刊例单价*时长*（以量制价折扣/10）*（折扣/10）*投放数量
4. 验证最低折后总价计算公式：刊例单价*时长*（以量制价折扣/10）*（最低折扣/10）*投放数量","1. 各参数设置成功
2. 全部折后总价自动计算显示
3. 计算结果与公式计算结果一致
4. 最低折后总价计算结果正确"
TC009,合同管理-投放信息,【服务费】服务费单价字段验证,P1,"管理员已登录系统;已配置价格管理信息","1. 观察服务费单价字段显示内容
2. 验证服务费单价取值来源
3. 尝试修改服务费单价字段，观察是否可编辑","1. 服务费单价字段正确显示对应媒体类型的服务费
2. 服务费单价取值为该媒体类型在价格设置中的服务费
3. 服务费单价字段不可修改"
TC010,合同管理-投放信息,【服务费】画面制作数量字段验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 观察画面制作数量字段的默认值
2. 在画面制作数量字段输入正整数""100""，观察输入结果
3. 在画面制作数量字段输入负数""-50""，观察输入限制
4. 在画面制作数量字段输入小数""100.5""，观察输入限制
5. 在画面制作数量字段输入""99999""，观察输入结果
6. 在画面制作数量字段输入""100000""，观察输入限制","1. 画面制作数量默认值等于该媒体类型的投放数量之和
2. 正整数""100""输入成功
3. 负数""-50""输入被拒绝，显示输入限制提示
4. 小数""100.5""输入被拒绝，显示正整数限制提示
5. ""99999""输入成功
6. ""100000""输入被拒绝，显示""最大输入99999""的提示"
TC011,合同管理-投放信息,【服务费】折扣字段基础验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 观察服务费折扣字段的必填状态和默认值
2. 点击服务费折扣下拉框，观察基础下拉选项
3. 选择不同的折扣值，观察选择结果","1. 服务费折扣字段为必填状态，默认展示10
2. 下拉框显示基础选项[10、8、6.4]
3. 不同折扣值选择成功"
TC012,合同管理-投放信息,【服务费】折扣字段代理商用户验证,P1,"代理商用户已登录系统;已配置媒体点位信息","1. 以代理商用户身份登录，选择媒体类型为12封灯箱，观察服务费折扣下拉选项
2. 选择媒体类型为18封灯箱，观察服务费折扣下拉选项
3. 选择媒体类型为看板，观察服务费折扣下拉选项
4. 选择媒体类型为普通媒体类型（非12封灯箱、18封灯箱、看板），观察服务费折扣下拉选项","1. 12封灯箱媒体类型显示折扣选项[10、8、6.4]
2. 18封灯箱媒体类型显示折扣选项[10、8、6.4]
3. 看板媒体类型显示折扣选项[10、8、6.4]
4. 普通媒体类型显示折扣选项[10、8]"
TC013,合同管理-投放信息,【服务费】折扣字段非代理商用户验证,P1,"非代理商用户已登录系统;已配置媒体点位信息","1. 以非代理商用户身份登录，选择媒体类型为12封灯箱，观察服务费折扣下拉选项
2. 选择媒体类型为18封灯箱，观察服务费折扣下拉选项
3. 选择媒体类型为看板，观察服务费折扣下拉选项
4. 选择媒体类型为普通媒体类型（非12封灯箱、18封灯箱、看板），观察服务费折扣下拉选项","1. 12封灯箱媒体类型显示折扣选项[10]
2. 18封灯箱媒体类型显示折扣选项[10]
3. 看板媒体类型显示折扣选项[10]
4. 普通媒体类型显示折扣选项[10]"
TC014,合同管理-投放信息,【服务费】折扣字段特殊公司类型验证,P1,"商业公司用户已登录系统;已配置媒体点位信息","1. 以商业公司用户身份登录，观察服务费折扣下拉选项
2. 以广告公司用户身份登录，观察服务费折扣下拉选项
3. 以非代理商、非商业公司、非广告公司用户身份登录，观察服务费折扣下拉选项","1. 商业公司用户显示折扣选项[10、8]
2. 广告公司用户显示折扣选项[10、8]
3. 其他公司类型用户显示折扣选项[10]"
TC015,合同管理-投放信息,【投放汇总】签约总价计算验证,P1,"管理员已登录系统;已配置价格管理信息","1. 设置发布费相关参数（全部折扣总价）
2. 设置服务费相关参数（服务费单价、画面制作数量、折扣）
3. 观察签约总价的自动计算
4. 验证计算公式：每一行各等级全部折扣总价+服务费单价*画面制作数量*（折扣/10）","1. 发布费参数设置成功
2. 服务费参数设置成功
3. 签约总价自动计算显示
4. 计算结果与公式计算结果一致"
TC016,合同管理-投放信息,【投放汇总】合计行计算验证,P1,"管理员已登录系统;已配置多行媒体点位信息","1. 观察合计行的投放数量计算
2. 观察合计行的发布费计算
3. 观察合计行的服务费计算
4. 观察合计行的签约总价计算","1. 投放数量等于当前列投放数量之和
2. 发布费等于全部折扣总价之和
3. 服务费等于（服务费单价*画面制作数量*折扣/10）
4. 签约总价等于当前列总价之和"
TC017,合同管理-投放信息,【投放明细】字段展示和编辑验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 切换到投放明细tab，观察字段展示
2. 验证编号、媒体类型、级别、线路、站点、刊例单价、投放数量等字段显示
3. 验证所有字段是否来自媒体选择自动带入
4. 尝试修改各个字段值，观察是否可编辑","1. 投放明细tab正确显示所有字段
2. 编号、媒体类型、级别、线路、站点、刊例单价、投放数量等字段正确显示
3. 所有字段值正确显示从媒体选择带入的数据
4. 所有字段均为只读状态，不可修改"
TC018,合同管理-投放信息,【投放明细】删除功能验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 在投放明细中点击某行的[删除]按钮
2. 观察弹出的二次确认弹窗内容
3. 在二次确认弹窗中点击[取消]按钮
4. 再次点击[删除]按钮，在确认弹窗中点击[确定]按钮
5. 观察删除后投放汇总列表的更新","1. 点击删除按钮后弹出二次确认弹窗
2. 弹窗询问是否确认删除该条记录
3. 点击取消后弹窗关闭，数据未删除，明细列表保持不变
4. 点击确定后该行数据被删除
5. 投放汇总列表相应更新，相关数据重新计算"
TC019,合同管理-投放信息,【其他信息】合同总额和发布时间验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 观察合同总额字段显示内容
2. 验证合同总额取值来源
3. 观察合同发布时间字段显示内容
4. 验证合同发布时间取值规则
5. 尝试修改合同总额和合同发布时间字段，观察是否可编辑","1. 合同总额字段正确显示数值
2. 合同总额等于汇总tab中的签约总价
3. 合同发布时间字段正确显示时间范围
4. 合同发布时间等于所有投放时间中最早时间-最晚时间
5. 两个字段均为只读状态，不可修改"
TC020,合同管理-投放信息,【其他信息】余额显示验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 设置合同类型为非储值合同，观察余额字段显示
2. 设置合同类型为储值合同，观察余额字段显示
3. 验证余额计算公式
4. 尝试修改余额字段，观察是否可编辑","1. 合同类型为非储值合同时，余额字段不显示
2. 合同类型为储值合同时，余额字段显示
3. 余额等于储值金额-合同总额
4. 余额字段为只读状态，不可修改"
TC021,合同管理-投放信息,【其他信息】付款方式验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 设置合同类型为非储值合同，观察付款方式字段状态和默认值
2. 点击付款方式下拉框，观察下拉选项
3. 设置合同类型为储值合同，观察付款方式字段状态和默认值
4. 点击付款方式下拉框，观察下拉选项","1. 合同类型为非储值合同时，付款方式为必填，默认展示先付/一次付清
2. 下拉选项显示[先付/一次付清、后付/一次付清、分批付款]
3. 合同类型为储值合同时，付款方式为必填，默认展示先付/一次付清
4. 下拉选项显示[先付/一次付清、分批付款]"
TC022,合同管理-投放信息,【其他信息】付款次数验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 选择付款方式为[先付/一次付清]，观察付款次数显示
2. 选择付款方式为[后付/一次付清]，观察付款次数显示
3. 选择付款方式为[分批付款]，观察付款次数显示和付款明细行数
4. 尝试修改付款次数，观察是否可编辑","1. 付款方式为先付/一次付清时，付款次数显示为1
2. 付款方式为后付/一次付清时，付款次数显示为1
3. 付款方式为分批付款时，付款次数显示为2，付款明细展示2行
4. 付款次数字段不可修改"
TC023,合同管理-投放信息,【其他信息】付款明细新增删除验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 选择付款方式为非分批付款，观察新增按钮显示
2. 选择付款方式为[分批付款]，观察新增按钮显示和删除按钮状态
3. 点击[新增]按钮添加付款明细行，观察删除按钮状态变化
4. 继续添加付款明细行，观察删除按钮状态
5. 删除付款明细行至剩余2行，观察删除按钮状态变化","1. 付款方式为非分批付款时，不显示新增按钮
2. 付款方式为分批付款时，显示新增按钮，初始2行不显示删除按钮
3. 添加第3行后，所有行都显示删除按钮
4. 继续添加行时，所有行都显示删除按钮
5. 删除至剩余2行时，删除按钮隐藏"
TC024,合同管理-投放信息,【其他信息】付款日期验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 观察付款日期字段的必填状态和默认值
2. 点击付款日期字段，观察日期选择框弹出
3. 选择任意日期，观察日期设置结果
4. 在分批付款模式下，设置第一行付款日期为""2024-01-01""
5. 设置第二行付款日期也为""2024-01-01""，观察重复日期提示
6. 修改第二行付款日期为""2024-01-02""，观察提示变化","1. 付款日期字段为必填状态，默认为当前日期
2. 日期选择框正常弹出
3. 日期设置成功
4. 第一行付款日期成功设置为""2024-01-01""
5. 第二行设置相同日期时显示重复日期提示
6. 修改为不同日期后，提示消失"
TC025,合同管理-投放信息,【其他信息】支付金额验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 观察支付金额字段的必填状态和默认值
2. 在支付金额字段输入正整数""5000""，观察输入结果
3. 在支付金额字段输入负数""-1000""，观察输入限制
4. 在支付金额字段输入超过合同金额的值，观察输入限制
5. 在分批付款模式下，修改第一行支付金额，观察第二行金额自动计算
6. 使多行金额之和不等于合同金额，观察验证提示","1. 支付金额字段为必填状态，默认为合同金额
2. 正整数""5000""输入成功
3. 负数""-1000""输入被拒绝，显示输入限制提示
4. 超过合同金额的值输入被拒绝，显示最大输入限制提示
5. 修改第一行后，第二行自动计算为合同金额-已输入金额
6. 金额之和不等于合同金额时，显示""支付金额应等于合同总额，请更改支付金额""的提示"
TC026,合同管理-投放信息,【其他信息】付款比例验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 观察付款比例字段的必填状态和默认值
2. 在单行付款模式下，观察付款比例显示
3. 在分批付款模式下，修改支付金额，观察付款比例自动计算
4. 验证付款比例计算公式：当前行支付金额/合同总额
5. 验证多行付款比例之和是否等于100%","1. 付款比例字段为必填状态，默认为100%
2. 单行付款时，付款比例显示100%
3. 分批付款时，修改支付金额后付款比例自动计算
4. 付款比例计算结果与公式计算结果一致
5. 多行付款比例之和等于100%"
TC027,合同管理-投放信息,【其他信息】附件上传验证,P1,管理员已登录系统,"1. 观察附件字段的必填状态
2. 点击附件上传区域，选择图片文件上传
3. 上传成功后点击图片，观察大图查看功能
4. 点击删除按钮，观察附件删除功能
5. 连续上传5个文件，观察上传结果
6. 尝试上传第6个文件，观察数量限制提示
7. 尝试上传非图片格式文件，观察格式限制","1. 附件字段为非必填状态
2. 图片文件成功上传，显示在附件列表中
3. 点击图片后弹出大图查看窗口
4. 附件成功删除，从列表中移除
5. 5个文件全部上传成功
6. 第6个文件上传失败，显示""最多只能上传5个文件""的提示
7. 非图片格式文件上传失败，显示格式限制提示"
TC028,合同管理-投放信息,【其他信息】备注字段验证,P1,管理员已登录系统,"1. 观察备注字段的必填状态和默认提示
2. 在备注字段输入""测试备注内容""，观察输入结果
3. 在备注字段输入100个字符，观察输入结果
4. 在备注字段输入101个字符，观察字符限制
5. 清空备注字段，观察默认提示恢复","1. 备注字段为非必填状态，默认显示""请输入""
2. ""测试备注内容""输入成功
3. 100个字符输入成功
4. 101个字符输入被限制，显示""最多输入100个字符""的提示
5. 清空后恢复显示""请输入""的默认提示"
TC029,合同管理-投放信息,【页面按钮】上一步功能验证,P1,"管理员已登录系统;已填写部分投放信息","1. 在投放信息页面填写部分信息后点击[上一步]按钮
2. 观察弹出的编辑未保存弹窗内容
3. 在弹窗中点击[取消]按钮
4. 再次点击[上一步]按钮，在弹窗中点击[确定]按钮
5. 从上一步页面返回，观察之前填写的信息是否保留","1. 点击上一步后弹出编辑未保存的确认弹窗
2. 弹窗询问是否确认离开当前页面
3. 点击取消后弹窗关闭，页面停留在当前页面，数据不发生变化
4. 点击确定后页面关闭，数据不保存，返回上一步页面
5. 从上一步返回时，填写的信息得到保留"
TC030,合同管理-投放信息,【页面按钮】下一步功能验证,P1,"管理员已登录系统;已填写投放信息","1. 设置发布费为0，服务费为0，点击[下一步]按钮
2. 设置发布费大于0，服务费为0，点击[下一步]按钮
3. 设置发布费为0，服务费大于0，点击[下一步]按钮
4. 设置发布费大于0，服务费大于0，点击[下一步]按钮","1. 显示""发布费、服务费不可都为0""的提示信息，无法进入下一步
2. 成功进入「合同复合」录入页面
3. 成功进入「合同复合」录入页面
4. 成功进入「合同复合」录入页面"
TC031,合同管理-投放信息,【投放档期】编辑功能验证,P1,"管理员已登录系统;已配置媒体点位信息","1. 在投放汇总中点击[投放档期]字段
2. 观察页面跳转到选择媒体编辑页面
3. 在选择媒体编辑页面修改投放档期信息
4. 保存修改后返回投放信息页面
5. 观察投放汇总页面信息更新","1. 点击投放档期字段后成功跳转
2. 页面正常跳转到选择媒体编辑页面
3. 投放档期信息修改成功
4. 修改信息保存成功，返回投放信息页面
5. 修改的信息正确更新到投放汇总页面"
TC032,合同管理-投放信息,【数据来源】客户报备进入验证,P1,"管理员已登录系统;已在客户报备中选择意向点位","1. 从客户报备中进入新增合同页面
2. 进入投放信息页面，观察投放汇总数据展示
3. 切换到投放明细页面，观察明细数据展示
4. 验证展示的数据是否为客户报备中选择的意向点位信息","1. 成功从客户报备进入新增合同页面
2. 投放汇总正确展示客户报备中选择的意向点位信息
3. 投放明细正确展示客户报备中选择的意向点位信息
4. 展示的数据与客户报备中选择的意向点位信息一致"
TC033,合同管理-投放信息,【数据来源】合同管理列表进入验证,P1,"管理员已登录系统;已从媒体选择中选择点位","1. 从合同管理列表点击新增进入新增合同页面
2. 进入投放信息页面，观察投放汇总数据展示
3. 切换到投放明细页面，观察明细数据展示
4. 验证展示的数据是否为媒体选择中选择的点位信息","1. 成功从合同管理列表进入新增合同页面
2. 投放汇总正确展示媒体选择中选择的点位信息
3. 投放明细正确展示媒体选择中选择的点位信息
4. 展示的数据与媒体选择中选择的点位信息一致" 