用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【查看按钮】显示条件和功能验证,P0,"1. 已登录系统
2. 进入合同管理列表页面
3. 准备不同状态的合同数据","1. 观察预定中状态合同的查看按钮显示
2. 观察已失效状态合同的查看按钮显示
3. 观察审批中状态合同的查看按钮显示
4. 观察已驳回状态合同的查看按钮显示
5. 观察已签约、执行中、已完结、作废中、已作废、终止中、已终止状态合同的查看按钮显示
6. 点击任意合同的查看按钮，观察页面跳转和数据展示","1. 预定中状态合同正确显示查看按钮
2. 已失效状态合同正确显示查看按钮
3. 审批中状态合同正确显示查看按钮
4. 已驳回状态合同正确显示查看按钮
5. 已签约、执行中、已完结、作废中、已作废、终止中、已终止状态合同均正确显示查看按钮
6. 成功进入合同查看页面，正确展示保存时录入的合同数据"
TC002,【删除按钮】显示条件验证,P0,"1. 已登录系统
2. 进入合同管理列表页面
3. 准备不同状态的合同数据","1. 观察预定中状态合同的删除按钮显示
2. 观察已撤回状态合同的删除按钮显示
3. 观察其他状态合同的删除按钮显示","1. 预定中状态合同正确显示删除按钮
2. 已撤回状态合同正确显示删除按钮
3. 其他状态合同不显示删除按钮"
TC003,【删除按钮】二次确认功能验证,P0,"1. 已登录系统
2. 进入合同管理列表页面
3. 存在预定中或已撤回状态的合同","1. 点击删除按钮，观察二次确认弹窗打开情况
2. 在确认弹窗中点击[取消]按钮，观察处理结果
3. 再次点击删除按钮，在确认弹窗中点击[确定]按钮，观察删除结果","1. 二次确认弹窗正常打开，提示删除确认信息
2. 点击取消后弹窗关闭，数据不发生变化，合同仍存在
3. 点击确定后当前合同被删除，合同中所有档期的广告位被释放"
TC004,【提交审批按钮】显示条件验证,P0,"1. 已登录系统
2. 进入合同管理列表页面
3. 准备不同状态的合同数据","1. 观察预订中状态合同的提交审批按钮显示
2. 观察已失效状态合同的提交审批按钮显示
3. 观察已驳回（不到3次）状态合同的提交审批按钮显示
4. 观察已驳回（已达3次）状态合同的提交审批按钮显示
5. 观察其他状态合同的提交审批按钮显示","1. 预订中状态合同正确显示提交审批按钮
2. 已失效状态合同正确显示提交审批按钮
3. 已驳回（不到3次）状态合同正确显示提交审批按钮
4. 已驳回（已达3次）状态合同不显示提交审批按钮
5. 其他状态合同不显示提交审批按钮"
TC005,【提交审批按钮】提交功能验证,P0,"1. 已登录系统
2. 存在显示提交审批按钮的合同
3. 合同为已提交状态","1. 点击提交审批按钮，观察提交审批处理过程
2. 观察提交审批验证的一致性","1. 提交合同审批成功，审批流程启动
2. 提交审批验证与在页面中提交的验证一致，验证规则相同"
TC006,【提交审批按钮】未提交合同编辑功能验证,P1,"1. 已登录系统
2. 存在显示提交审批按钮的合同
3. 合同为未提交状态","1. 点击未提交合同的提交审批按钮，观察页面跳转
2. 观察编辑页面的信息展示
3. 观察编辑页面与新增页面的一致性
4. 观察数据回显的准确性","1. 成功进入编辑页面，页面跳转正常
2. 编辑页面正确展示合同信息
3. 编辑页面与新增页面信息一致，界面布局和字段相同
4. 数据回显正确，所有字段值与原合同数据一致"
TC007,【编辑按钮】显示条件验证,P1,"1. 已登录系统
2. 进入合同管理列表页面
3. 准备不同状态的合同数据","1. 观察已驳回状态合同的编辑按钮显示
2. 观察已撤回状态合同的编辑按钮显示
3. 观察已失效状态合同的编辑按钮显示
4. 观察其他状态合同的编辑按钮显示","1. 已驳回状态合同正确显示编辑按钮
2. 已撤回状态合同正确显示编辑按钮
3. 已失效状态合同正确显示编辑按钮
4. 其他状态合同不显示编辑按钮"
TC008,【编辑按钮】编辑页面功能验证,P0,"1. 已登录系统
2. 存在显示编辑按钮的合同","1. 点击编辑按钮，观察页面跳转和信息回显
2. 观察编辑页面中操作按钮的显示","1. 成功进入编辑页面，信息回显正确，所有合同数据准确显示
2. 编辑页面正确显示取消、提交审批按钮" 