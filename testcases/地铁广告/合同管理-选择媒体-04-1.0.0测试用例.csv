用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【媒体组合媒体切换】基本切换功能验证,P0,"1. 已登录系统
2. 进入合同管理-选择媒体页面","1. 观察页面默认选中状态
2. 点击切换到组合媒体，观察页面变化
3. 点击切换回媒体，观察页面变化","1. 页面默认选中媒体选项
2. 成功切换到组合媒体选择界面，相关筛选条件和列表内容更新
3. 成功切换回媒体选择界面，恢复媒体相关的筛选条件和列表"
TC002,【媒体选择-所属线路】多选和排序功能验证,P0,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 当前选择媒体模式","1. 观察所属线路的默认选择状态
2. 点击选择多条线路，观察多选功能
3. 点击[全选]按钮，观察全选功能
4. 点击[全不选]按钮，观察全不选功能
5. 观察线路的排序规则","1. 默认选择最小号线路
2. 可以选择多条线路，选择状态正确显示
3. 所有线路被选中，全选功能正常
4. 所有线路被取消选择，全不选功能正常
5. 线路按照数字由小到大排序，纯线路随机排序"
TC003,【媒体选择-全线同播】特殊显示规则验证,P1,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 当前选择媒体模式","1. 只选择全线同播线路，观察界面显示变化
2. 观察所在位置和站点等级筛选项的显示状态
3. 观察刊例单价列表的显示情况
4. 观察右侧点位图的显示内容","1. 选择全线同播后界面相应更新
2. 不显示所在位置、站点等级筛选项
3. 显示刊例单价列表
4. 右侧点位图不显示站点信息"
TC004,【媒体选择-全线同播和其他混选】显示规则验证,P1,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 当前选择媒体模式","1. 同时选择全线同播和其他线路，观察界面显示变化
2. 观察所在位置和站点等级筛选项的显示状态","1. 混选全线同播和其他线路后界面相应更新
2. 显示所在位置、站点等级筛选项"
TC005,【媒体选择-媒体类型大类】筛选功能验证,P0,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 当前选择媒体模式","1. 观察媒体类型大类的默认选择状态
2. 点击选择多个媒体类型大类，观察多选功能
3. 点击[全选]按钮，观察全选功能
4. 点击[全不选]按钮，观察全不选功能
5. 观察媒体类型大类的排序规则","1. 默认选择最早创建的媒体类型
2. 可以选择多个媒体类型大类，选择状态正确显示
3. 所有媒体类型大类被选中，全选功能正常
4. 所有媒体类型大类被取消选择，全不选功能正常
5. 媒体类型大类按照创建时间正序排列"
TC006,【媒体选择-包车组件】特殊处理验证,P1,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 当前选择媒体模式
4. 媒体类型中包含包车组件","1. 选择包车组件媒体类型，观察界面显示变化
2. 观察所在位置和站点等级筛选项的显示状态
3. 观察刊例单价列表的显示情况","1. 选择包车组件后界面相应更新
2. 不显示所在位置、站点等级筛选项
3. 显示刊例单价列表"
TC007,【媒体选择-媒体类型小类】筛选功能验证,P1,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 当前选择媒体模式","1. 观察媒体类型小类的默认选择状态
2. 点击选择多个媒体类型小类，观察多选功能
3. 点击[全选]按钮，观察全选功能
4. 点击[全不选]按钮，观察全不选功能
5. 观察媒体类型小类的排序规则","1. 默认选择最早创建的媒体小类
2. 可以选择多个媒体类型小类，选择状态正确显示
3. 所有媒体类型小类被选中，全选功能正常
4. 所有媒体类型小类被取消选择，全不选功能正常
5. 媒体类型小类按照创建时间正序排列"
TC008,【媒体选择-所在位置和站点等级】筛选功能验证,P1,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 当前选择媒体模式
4. 已选择非全线同播和非包车组件的线路和媒体类型","1. 观察所在位置的默认选择状态和排序
2. 点击选择多个所在位置，观察多选功能
3. 点击[全选]按钮，观察所在位置的全选功能
4. 点击[全不选]按钮，观察所在位置的全不选功能
5. 观察站点等级的默认选择状态和排序
6. 点击选择多个站点等级，观察多选功能
7. 点击[全选]按钮，观察站点等级的全选功能
8. 点击[全不选]按钮，观察站点等级的全不选功能","1. 所在位置默认选择所有位置，排序和下拉列表一致
2. 可以选择多个所在位置，选择状态正确显示
3. 所有所在位置被选中，全选功能正常
4. 所有所在位置被取消选择，全不选功能正常
5. 站点等级默认选择所有等级，排序为S、A++、A
6. 可以选择多个站点等级，选择状态正确显示
7. 所有站点等级被选中，全选功能正常
8. 所有站点等级被取消选择，全不选功能正常"
TC009,【媒体选择-刊例单价列表】显示和字段验证,P0,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 已选择包车组件、PIS或物理库存不为1的媒体","1. 观察刊例单价列表的显示条件
2. 观察列表中媒体类型字段的显示内容
3. 观察列表中刊例单价字段的显示内容
4. 观察列表的排序规则","1. 选择媒体类型包车组件、PIS、物理库存不为1的媒体时显示刊例单价列表
2. 媒体类型字段正确显示对应的媒体类型名称
3. 
1、普通媒体显示当前媒体在价格管理中设置的刊例单价
2、PIS、包车组件显示适用线别下设置的刊例单价
4. 列表按照媒体列表的顺序展示"
TC010,【媒体选择-包车组件选择】弹窗和库存验证,P1,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 刊例单价列表中有包车组件媒体","1. 点击包车组件的[选择]按钮，观察选择弹窗打开情况
2. 观察弹窗中包车组件二级类型的可选项
3. 观察当前媒体库存量的显示
4. 在投放数量字段输入小于库存的正整数，点击确定
5. 在投放数量字段输入等于库存的正整数，点击确定
6. 在投放数量字段输入大于库存的数量，点击确定","1. 选择弹窗正常打开，显示包车组件的选择界面
2. 可选项包含车门、看板框架、座椅三角护板（车贴）、窗户、座椅护板、座椅护板（宽版）、座椅护板（窄款）、拉手、地面贴画、顶帖、墙面贴等二级类型
3. 正确显示当前媒体的库存量
4. 输入小于库存的数量可成功保存并确认
5. 输入等于库存的数量可成功保存并确认
6. 输入大于库存的数量保存失败，显示相应错误提示"
TC011,【媒体选择-PIS媒体选择】时长验证,P1,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 刊例单价列表中有PIS媒体
4. 已选择投放档期","1. 点击PIS媒体的[选择]按钮，观察选择弹窗打开情况
2. 观察空闲可售卖时长的计算和显示
3. 在投放时长字段输入小于等于可售卖时长的正整数，点击确定
4. 在投放时长字段输入大于可售卖时长的数量，观察输入限制
5. 点击[取消]按钮，观察弹窗关闭情况","1. 选择弹窗正常打开，显示PIS媒体的选择界面
2. 正确显示空闲可售卖时长=该投放档期每天的可售卖时长-当天已锁位或已售出的投放时长
3. 输入小于等于可售卖时长的数量可成功确认，自动带入单位s
4. 输入大于可售卖时长的数量无法输入或显示错误提示
5. 点击取消后弹窗关闭，回到媒体选择弹窗，数据不保存"
TC012,【组合媒体选择-所属线路】筛选功能验证,P0,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 切换到组合媒体选择模式","1. 观察组合媒体所属线路的默认选择状态
2. 点击选择多条线路，观察多选功能
3. 点击[全选]按钮，观察全选功能
4. 点击[全不选]按钮，观察全不选功能
5. 选择全线同播线路，观察特殊显示规则","1. 默认选择最小号线路
2. 可以选择多条线路，选择状态正确显示
3. 所有线路被选中，全选功能正常
4. 所有线路被取消选择，全不选功能正常
5. 选择全线同播时不显示所在位置、站点等级，显示刊例单价列表"
TC013,【组合媒体选择-媒体场景】单选功能验证,P1,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 切换到组合媒体选择模式
4. 已选择线路","1. 观察媒体场景的默认选择状态
2. 点击选择不同的媒体场景选项，观察单选功能","1. 默认选择平面媒体场景
2. 每次只能选择一个媒体场景，选择新的场景时自动取消之前的选择"
TC014,【组合媒体选择-刊例单价列表】显示和操作验证,P0,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 切换到组合媒体选择模式
4. 已选择媒体类型为列车媒体且物理库存不为1的组合媒体","1. 观察刊例单价列表的显示条件和字段
2. 观察列表的排序规则
3. 点击[选择]按钮，观察选择弹窗打开情况
4. 观察当前空闲物理库存量的计算显示","1. 选择媒体类型=列车媒体、物理库存不为1的组合媒体时显示刊例单价列表，包含媒体类型和刊例单价字段
2. 列表按照资产包里媒体和组合媒体的创建时间倒序排列
3. 选择弹窗正常打开，显示组合媒体的选择界面
4. 正确显示当前空闲物理库存量的剩余库存量，包含半包车和全包车的库存计算"
TC015,【组合媒体选择-库存量验证】投放数量校验,P1,"1. 已登录系统
2. 进入合同管理-选择媒体页面
3. 已打开组合媒体选择弹窗","1. 在投放数量字段输入小于库存的正整数，点击确定
2. 在投放数量字段输入等于库存的正整数，点击确定
3. 在投放数量字段输入大于库存的数量，观察输入结果
4. 在投放数量字段输入非正整数，观察输入限制","1. 输入小于库存的数量可成功保存并确认
2. 输入等于库存的数量可成功保存并确认
3. 输入大于库存的数量输入失败，显示相应错误提示
4. 只能输入正整数，其他格式输入被限制或提示错误" 