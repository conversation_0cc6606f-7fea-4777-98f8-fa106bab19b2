用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【合同生成规则】新建车务订单应用规则验证,P0,已配置对应业务类型的合同生成规则,"1. 新建[线上抵押]订单，填写必要信息并保存，观察合同生成规则应用情况
2. 新建[线上抵押数据推送]订单，填写必要信息并保存，观察合同生成规则应用情况
3. 新建[线上解押]订单，填写必要信息并保存，观察合同生成规则应用情况
4. 新建[预备线上解押]订单，填写必要信息并保存，观察合同生成规则应用情况","1. 线上抵押订单成功应用对应的合同生成规则
2. 线上抵押数据推送订单成功应用对应的合同生成规则
3. 线上解押订单成功应用对应的合同生成规则
4. 预备线上解押订单成功应用对应的合同生成规则"
TC002,【合同生成规则】代下车务订单应用规则验证,P1,已配置对应业务类型的合同生成规则,"1. 代下[线上抵押]订单，填写必要信息并保存，观察合同生成规则应用情况
2. 代下[线上抵押数据推送]订单，填写必要信息并保存，观察合同生成规则应用情况
3. 代下[线上解押]订单，填写必要信息并保存，观察合同生成规则应用情况
4. 代下[预备线上解押]订单，填写必要信息并保存，观察合同生成规则应用情况","1. 代下线上抵押订单成功应用对应的合同生成规则
2. 代下线上抵押数据推送订单成功应用对应的合同生成规则
3. 代下线上解押订单成功应用对应的合同生成规则
4. 代下预备线上解押订单成功应用对应的合同生成规则"
TC003,【合同生成规则】修改车务订单应用规则验证,P1,"已配置对应业务类型的合同生成规则;存在各类型车务订单","1. 修改[线上抵押]订单信息并保存，观察合同生成规则应用情况
2. 修改[线上抵押数据推送]订单信息并保存，观察合同生成规则应用情况
3. 修改[线上解押]订单信息并保存，观察合同生成规则应用情况
4. 修改[预备线上解押]订单信息并保存，观察合同生成规则应用情况","1. 修改线上抵押订单后成功应用对应的合同生成规则
2. 修改线上抵押数据推送订单后成功应用对应的合同生成规则
3. 修改线上解押订单后成功应用对应的合同生成规则
4. 修改预备线上解押订单后成功应用对应的合同生成规则"
TC004,【合同生成规则】接口下单应用规则验证,P0,已配置对应业务类型的合同生成规则,"1. 通过接口创建易鑫非新安线上抵押订单，观察合同生成规则应用情况
2. 通过接口创建易鑫新安线上抵押订单，观察合同生成规则应用情况
3. 通过接口创建普通客户线上抵押订单，观察合同生成规则应用情况
4. 通过接口创建普通客户线上解押订单，观察合同生成规则应用情况
5. 通过接口创建安吉预备线解押订单，观察合同生成规则应用情况
6. 通过接口创建车晓线上抵押数据推送订单，观察合同生成规则应用情况","1. 易鑫非新安线上抵押订单成功应用对应的合同生成规则
2. 易鑫新安线上抵押订单成功应用对应的合同生成规则
3. 普通客户线上抵押订单成功应用对应的合同生成规则
4. 普通客户线上解押订单成功应用对应的合同生成规则
5. 安吉预备线解押订单成功应用对应的合同生成规则
6. 车晓线上抵押数据推送订单成功应用对应的合同生成规则"
TC005,【合同生成规则】接口修改订单应用规则验证,P1,"已配置对应业务类型的合同生成规则;存在对应的接口订单","1. 通过接口修改易鑫非新安线上抵押订单，观察合同生成规则应用情况
2. 通过接口修改易鑫新安线上抵押订单，观察合同生成规则应用情况
3. 通过接口修改安吉预备线解押订单，观察合同生成规则应用情况","1. 修改易鑫非新安线上抵押订单后成功应用对应的合同生成规则
2. 修改易鑫新安线上抵押订单后成功应用对应的合同生成规则
3. 修改安吉预备线解押订单后成功应用对应的合同生成规则"
TC006,【合同生成规则】提交和返回按钮功能验证,P1,,"1. 在新增合同生成规则页面，填写所有必填字段信息
2. 点击[提交]按钮，观察页面跳转和数据保存情况
3. 在新增合同生成规则页面，点击[返回]按钮，观察页面跳转情况","1. 所有必填字段填写完整
2. 页面跳转到【合同生成规则】页面，生成1条新的合同生成规则记录
3. 页面返回到上一页"
TC007,【合同生成规则】修改功能验证,P1,存在合同生成规则数据,"1. 进入修改合同生成规则页面，观察页面字段显示
2. 修改规则名称等可编辑字段
3. 点击[提交]按钮保存修改","1. 页面显示与新增合同生成规则页面一致的字段和功能
2. 可编辑字段允许修改，原有数据正确回显
3. 修改保存成功，数据更新"
TC008,【客户合同生成规则】页面显示验证,P1,,"1. 进入客户合同生成规则页面，查看页面标题显示
2. 查看搜索框字段显示
3. 查看列表字段显示
4. 查看数据显示内容和排序","1. 页面标题显示「首页/客户合同生成规则」
2. 搜索框显示客户名称、子公司名称、抵押权人、规则名称、业务类型字段
3. 列表显示客户名称、子公司名称、抵押权人、办理地点、业务类型、合同生成规则、操作字段
4. 显示合同生成规则页面的应用范围数据，按新增时间倒序排列"
TC009,【客户合同生成规则】客户名称搜索验证,P1,存在客户合同生成规则数据,"1. 在客户名称搜索框中输入存在的客户名称左半部分，点击查询，观察搜索结果
2. 在客户名称搜索框中输入存在的客户名称右半部分，点击查询，观察搜索结果
3. 在客户名称搜索框中输入完整的客户名称，点击查询，观察搜索结果","1. 列表显示包含该左半部分客户名称的所有符合条件数据
2. 列表显示包含该右半部分客户名称的所有符合条件数据
3. 列表显示该完整客户名称的所有符合条件数据"
TC010,【客户合同生成规则】子公司名称搜索验证,P1,存在客户合同生成规则数据,"1. 在子公司名称搜索框中输入存在的子公司名称左半部分，点击查询，观察搜索结果
2. 在子公司名称搜索框中输入存在的子公司名称右半部分，点击查询，观察搜索结果
3. 在子公司名称搜索框中输入完整的子公司名称，点击查询，观察搜索结果","1. 列表显示包含该左半部分子公司名称的所有符合条件数据
2. 列表显示包含该右半部分子公司名称的所有符合条件数据
3. 列表显示该完整子公司名称的所有符合条件数据"
TC011,【客户合同生成规则】抵押权人搜索验证,P1,存在客户合同生成规则数据,"1. 在抵押权人搜索框中输入存在的抵押权人左半部分，点击查询，观察搜索结果
2. 在抵押权人搜索框中输入存在的抵押权人右半部分，点击查询，观察搜索结果
3. 在抵押权人搜索框中输入完整的抵押权人，点击查询，观察搜索结果","1. 列表显示包含该左半部分抵押权人的所有符合条件数据
2. 列表显示包含该右半部分抵押权人的所有符合条件数据
3. 列表显示该完整抵押权人的所有符合条件数据"
TC012,【客户合同生成规则】规则名称搜索验证,P1,存在客户合同生成规则数据,"1. 在规则名称搜索框中输入存在的规则名称左半部分，点击查询，观察搜索结果
2. 在规则名称搜索框中输入存在的规则名称右半部分，点击查询，观察搜索结果
3. 在规则名称搜索框中输入完整的规则名称，点击查询，观察搜索结果","1. 列表显示包含该左半部分规则名称的所有符合条件数据
2. 列表显示为空
3. 列表显示该完整规则名称的所有符合条件数据"
TC013,【客户合同生成规则】业务类型搜索验证,P1,存在客户合同生成规则数据,"1. 点击业务类型下拉框，查看所有可选项
2. 选择「线上抵押」业务类型，点击查询，观察搜索结果
3. 选择「线上解押」业务类型，点击查询，观察搜索结果
4. 选择「线上抵押数据推送」业务类型，点击查询，观察搜索结果
5. 选择「预备线上解押」业务类型，点击查询，观察搜索结果","1. 下拉框显示「全部」、「线上抵押」、「线上解押」、「线上抵押数据推送」、「预备线上解押」选项
2. 列表显示所有线上抵押业务类型的符合条件数据
3. 列表显示所有线上解押业务类型的符合条件数据
4. 列表显示所有线上抵押数据推送业务类型的符合条件数据
5. 列表显示所有预备线上解押业务类型的符合条件数据"
TC014,【客户合同生成规则】组合查询验证,P1,存在客户合同生成规则数据,"1. 在搜索框中输入客户名称、子公司名称、抵押权人、规则名称，选择业务类型，点击[查询]按钮，观察搜索结果
2. 点击[重置]按钮，观察页面数据变化","1. 列表显示同时满足所有搜索条件的符合条件数据
2. 所有搜索条件清空，列表显示全部数据"
TC015,【客户合同生成规则】删除按钮功能验证,P1,存在客户合同生成规则数据,"1. 点击任意一条记录的[删除]按钮，观察弹窗显示
2. 在删除确认弹窗中点击[取消]按钮，观察弹窗和数据变化
3. 再次点击[删除]按钮，在删除确认弹窗中点击[确定]按钮，观察弹窗和数据变化","1. 显示删除确认弹窗，提示文字为「您确定要删除该应用范围吗？」
2. 弹窗关闭，该条数据未被删除，仍显示在列表中
3. 弹窗关闭，该条数据被删除，不再显示在列表中"
TC016,【客户合同生成规则】详情按钮功能验证,P1,存在客户合同生成规则数据,"1. 点击任意一条记录的[详情]按钮，观察页面跳转情况","1. 页面跳转到对应的【合同生成规则详情】页面，显示该规则的详细信息"
TC017,【合同生成规则】无对应规则时订单处理验证,P2,未配置对应业务类型的合同生成规则,"1. 新建[线上抵押]订单，填写必要信息并保存，观察系统处理情况
2. 通过接口创建线上解押订单，观察系统处理情况","1. 系统提示无对应的合同生成规则或使用默认处理方式
2. 系统提示无对应的合同生成规则或使用默认处理方式"
TC018,【合同生成规则】默认规则与特殊规则优先级验证,P0,"配置了默认规则和特殊规则的业务类型;存在符合特殊规则条件的订单","1. 创建符合特殊规则条件的订单，观察应用的合同生成规则
2. 创建不符合特殊规则条件的订单，观察应用的合同生成规则","1. 订单应用特殊规则，而非默认规则
2. 订单应用默认规则"
TC019,【合同生成规则】规则应用范围边界验证,P2,"配置了特定条件的合同生成规则;存在边界条件的订单数据","1. 创建刚好符合规则条件的订单，观察规则应用情况
2. 创建刚好不符合规则条件的订单，观察规则应用情况","1. 订单成功应用对应的合同生成规则
2. 订单不应用该规则，使用默认规则或提示无匹配规则"
TC020,【合同生成规则】数据一致性验证,P1,"存在客户合同生成规则数据;存在合同生成规则数据","1. 在合同生成规则页面查看规则配置信息
2. 在客户合同生成规则页面查看对应的应用范围信息
3. 修改合同生成规则，观察客户合同生成规则页面数据变化","1. 合同生成规则页面显示完整的规则配置信息
2. 客户合同生成规则页面显示对应规则的应用范围信息，数据一致
3. 客户合同生成规则页面的相关数据同步更新"
