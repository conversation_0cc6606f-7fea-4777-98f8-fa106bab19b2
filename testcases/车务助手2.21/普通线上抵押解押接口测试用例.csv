用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【普通线上抵押解押接口】所有人现场照配置规则验证,P0,"业务类型为线上抵押或线上解押","1. 在未设置现场照配置的情况下，不上传现场照进行下单，观察下单结果
2. 设置现场照配置为选填，客户不传现场照进行下单，观察签署流程
3. 设置现场照配置为选填，客户已上传现场照进行下单，观察签署流程","1. 下单失败，提示需要上传现场照
2. 下单成功，车主签署合同时需要上传现场照
3. 下单成功，直接进入签署合同流程"
TC002,【普通线上抵押解押接口】所有人现场照格式和大小验证,P0,"业务类型为线上抵押或线上解押","1. 上传500kb以下的jpg格式现场照文件，进行下单操作
2. 上传500kb以下的png格式现场照文件，进行下单操作
3. 上传500kb以下的其他格式现场照文件，进行下单操作
4. 上传500kb以上的jpg格式现场照文件，进行下单操作","1. 下单成功，现场照文件正常上传
2. 下单失败，提示文件格式不支持
3. 下单失败，提示文件格式不支持
4. 下单失败，提示文件大小超出限制"
TC003,【普通线上抵押解押接口】抵押合同文件上传验证,P0,"业务类型为线上抵押;合同生成规则为上传已签署文件或上传文件在线签署","1. 不上传抵押合同文件进行下单，观察下单结果
2. 上传700kb以下的pdf格式抵押合同文件，进行下单操作
3. 上传700kb以下的doc格式抵押合同文件，进行下单操作
4. 上传700kb以上的pdf格式抵押合同文件，进行下单操作
5. 在线上解押业务中上传抵押合同文件，观察系统处理","1. 下单失败，提示需要上传抵押合同文件
2. 下单成功，抵押合同文件正常上传
3. 下单失败，提示文件格式不支持
4. 下单失败，提示文件大小超出限制
5. 系统直接忽略该文件，不影响下单流程"
TC004,【普通线上抵押解押接口】机动车抵押登记申请表文件上传验证,P0,"业务类型为线上抵押或线上解押;合同生成规则为上传已签署文件或上传文件在线签署","1. 不上传机动车抵押登记申请表文件进行下单，观察下单结果
2. 上传600kb以下的pdf格式申请表文件，进行下单操作
3. 上传600kb以下的doc格式申请表文件，进行下单操作
4. 上传600kb以上的pdf格式申请表文件，进行下单操作","1. 下单失败，提示需要上传机动车抵押登记申请表文件
2. 下单成功，申请表文件正常上传
3. 下单失败，提示文件格式不支持
4. 下单失败，提示文件大小超出限制"
TC005,【普通线上抵押解押接口】委托书文件上传验证,P0,"业务类型为线上抵押或线上解押;合同生成规则为上传已签署文件或上传文件在线签署","1. 不上传车主委托书文件进行下单，观察下单结果
2. 不上传抵押权人委托书文件进行下单，观察下单结果
3. 上传500kb以下的pdf格式车主委托书文件，进行下单操作
4. 上传500kb以下的pdf格式抵押权人委托书文件，进行下单操作
5. 上传500kb以下的doc格式委托书文件，进行下单操作
6. 上传500kb以上的pdf格式委托书文件，进行下单操作","1. 下单失败，提示需要上传车主委托书文件
2. 下单失败，提示需要上传抵押权人委托书文件
3. 下单成功，车主委托书文件正常上传
4. 下单成功，抵押权人委托书文件正常上传
5. 下单失败，提示文件格式不支持
6. 下单失败，提示文件大小超出限制"
TC006,【普通线上抵押解押接口】所有人声明书和抵押物清单文件上传验证,P0,"业务类型为线上抵押;合同生成规则为上传已签署文件或上传文件在线签署","1. 不上传所有人声明书文件进行下单，观察下单结果
2. 不上传抵押物清单文件进行下单，观察下单结果
3. 上传500kb以下的pdf格式所有人声明书文件，进行下单操作
4. 上传500kb以下的pdf格式抵押物清单文件，进行下单操作
5. 上传500kb以下的doc格式声明书文件，进行下单操作
6. 上传500kb以上的pdf格式声明书文件，进行下单操作
7. 在线上解押业务中上传所有人声明书和抵押物清单文件，观察系统处理","1. 下单失败，提示需要上传所有人声明书文件
2. 下单失败，提示需要上传抵押物清单文件
3. 下单成功，所有人声明书文件正常上传
4. 下单成功，抵押物清单文件正常上传
5. 下单失败，提示文件格式不支持
6. 下单失败，提示文件大小超出限制
7. 系统直接忽略这些文件，不影响下单流程"
TC007,【普通线上抵押解押接口】业务类型差异化处理验证,P1,"存在线上抵押和线上解押业务数据","1. 创建线上抵押订单，验证需要上传的文件类型和数量
2. 创建线上解押订单，验证需要上传的文件类型和数量
3. 在线上解押订单中尝试上传抵押合同、所有人声明书、抵押物清单，观察系统处理","1. 线上抵押订单需要上传：现场照、抵押合同、申请表、委托书、声明书、抵押物清单
2. 线上解押订单需要上传：现场照、申请表、委托书
3. 系统忽略线上解押业务中的抵押合同、所有人声明书、抵押物清单文件"
TC008,【普通线上抵押解押接口】文件上传边界值验证,P2,"合同生成规则为上传已签署文件或上传文件在线签署","1. 上传499kb的jpg格式现场照文件，进行下单操作
2. 上传501kb的jpg格式现场照文件，进行下单操作
3. 上传699kb的pdf格式抵押合同文件，进行下单操作
4. 上传701kb的pdf格式抵押合同文件，进行下单操作
5. 上传599kb的pdf格式申请表文件，进行下单操作
6. 上传601kb的pdf格式申请表文件，进行下单操作","1. 现场照文件上传成功，下单正常进行
2. 现场照文件上传失败，提示文件大小超出限制
3. 抵押合同文件上传成功，下单正常进行
4. 抵押合同文件上传失败，提示文件大小超出限制
5. 申请表文件上传成功，下单正常进行
6. 申请表文件上传失败，提示文件大小超出限制"
TC009,【普通线上抵押解押接口】完整下单流程验证,P0,"业务类型为线上抵押;现场照配置为选填;合同生成规则为上传已签署文件","1. 按要求上传所有必需文件（现场照、抵押合同、申请表、车主委托书、抵押权人委托书、所有人声明书、抵押物清单），进行完整下单流程
2. 验证下单成功后的订单状态和文件关联情况","1. 下单成功，系统生成订单编号，所有上传文件正确关联到订单
2. 订单状态为待处理，所有文件可以正常查看和下载"
TC010,【普通线上抵押解押接口】接口参数验证,P1,,"1. 使用正确的接口参数调用普通线上抵押接口，观察接口响应
2. 使用缺少必填参数的请求调用接口，观察接口响应
3. 使用错误格式参数调用接口，观察接口响应
4. 使用超长参数值调用接口，观察接口响应","1. 接口返回成功状态码，响应数据格式正确
2. 接口返回参数错误提示，明确指出缺少的必填参数
3. 接口返回参数格式错误提示
4. 接口返回参数长度超限错误提示"
TC011,【普通线上抵押解押接口】并发请求处理验证,P2,,"1. 同时发起多个普通线上抵押下单请求，观察系统处理情况
2. 在文件上传过程中同时发起多个请求，观察系统响应
3. 验证并发情况下的数据一致性","1. 系统正确处理并发请求，每个请求都得到正确响应
2. 文件上传过程中的并发请求得到适当处理，不会导致文件丢失或重复
3. 并发操作后数据保持一致性，无数据冲突"
TC012,【普通线上抵押解押接口】接口响应时间验证,P2,,"1. 调用普通线上抵押接口，记录接口响应时间
2. 在大文件上传场景下调用接口，记录响应时间
3. 验证接口响应时间是否在合理范围内","1. 正常情况下接口响应时间在3秒以内
2. 大文件上传场景下接口响应时间在10秒以内
3. 所有接口响应时间符合性能要求"
TC013,【普通线上抵押解押接口】异常情况处理验证,P2,,"1. 在网络不稳定环境下调用接口，观察接口处理情况
2. 在文件上传过程中模拟网络中断，观察系统恢复机制
3. 验证接口的错误处理和重试机制","1. 网络不稳定时接口返回适当的错误提示
2. 网络中断后系统提供文件上传恢复机制
3. 接口具备合理的错误处理和重试机制"
TC014,【普通线上抵押解押接口】权限验证,P1,,"1. 使用有权限的用户账号调用普通线上抵押接口，观察接口响应
2. 使用无权限的用户账号调用接口，观察接口响应
3. 使用过期token调用接口，观察接口响应","1. 有权限用户可以正常调用接口，返回成功响应
2. 无权限用户调用接口返回权限不足错误提示
3. 过期token调用接口返回认证失败错误提示"
TC015,【普通线上抵押解押接口】数据安全性验证,P1,"存在敏感业务数据","1. 调用接口查看返回的数据内容，检查敏感信息处理
2. 验证文件上传过程中的数据加密传输
3. 检查接口日志中的敏感数据记录情况","1. 接口返回数据中敏感信息进行适当脱敏处理
2. 文件上传过程使用加密传输，保证数据安全
3. 接口日志不记录敏感数据明文信息"
