用例编号,用例标题,重要程度,前置条件,测试步骤,预期结果
TC001,【修改车务订单】合同生成方式显示条件验证,P1,"存在不同业务类型的车务订单","1. 进入线上抵押业务订单的修改页面，查看合同生成方式字段显示情况
2. 进入线上解押业务订单的修改页面，查看合同生成方式字段显示情况
3. 进入线上抵押数据推送业务订单的修改页面，查看合同生成方式字段显示情况
4. 进入其他业务类型订单的修改页面，查看合同生成方式字段显示情况","1. 线上抵押订单显示：抵押合同、委托书、申请表、所有人声明书、抵押物清单生成方式字段
2. 线上解押订单显示：委托书、申请表生成方式字段，不显示抵押合同、所有人声明书、抵押物清单字段
3. 线上抵押数据推送订单显示：抵押合同、委托书、申请表、所有人声明书、抵押物清单生成方式字段
4. 其他业务类型订单不显示合同生成方式相关字段"
TC002,【修改车务订单】合同生成方式编辑权限验证,P0,"存在设置了不同合同生成规则的订单","1. 查看合同生成规则设置为「上传已签署文件」的订单，尝试修改生成方式选项
2. 查看合同生成规则设置为「上传文件在线签署」的订单，尝试修改生成方式选项
3. 查看合同生成规则设置为「模版合同在线签署」的订单，尝试修改生成方式选项
4. 验证各种生成方式下的功能描述和参与人、签章位置关键字显示","1. 上传已签署文件选项被选中且不可修改，显示需要上传pdf文件，不参与电子合同签署流程
2. 上传文件在线签署选项被选中且不可修改，显示需要上传pdf文件，展示参与人、签章位置关键字，走线上签署流程
3. 模版合同在线签署选项被选中且不可修改，显示系统自动灌数生成电子合同，走线上签署流程
4. 各生成方式的功能描述和相关字段显示正确"
TC003,【修改车务订单】合同文件大小验证,P0,"合同生成方式为上传已签署文件或上传文件在线签署","1. 上传大于700KB的抵押合同文件，观察系统提示
2. 上传700KB及以下的抵押合同文件，观察上传结果
3. 上传大于600KB的申请表文件，观察系统提示
4. 上传600KB及以下的申请表文件，观察上传结果
5. 上传大于500KB的委托书文件，观察系统提示
6. 上传500KB及以下的委托书文件，观察上传结果
7. 上传大于500KB的所有人声明书文件，观察系统提示
8. 上传500KB及以下的所有人声明书文件，观察上传结果
9. 上传大于500KB的抵押物清单文件，观察系统提示
10. 上传500KB及以下的抵押物清单文件，观察上传结果","1. 提示「请上传700KB以下的文件」，上传失败
2. 抵押合同文件上传成功
3. 提示「请上传600KB以下的文件」，上传失败
4. 申请表文件上传成功
5. 提示「请上传500KB以下的文件」，上传失败
6. 委托书文件上传成功
7. 提示「请上传500KB以下的文件」，上传失败
8. 所有人声明书文件上传成功
9. 提示「请上传500KB以下的文件」，上传失败
10. 抵押物清单文件上传成功"
TC004,【修改车务订单】模版合同字段变更重签验证,P0,"6个合同文件均设置为模版合同在线签署;6个合同文件中均设置了车牌号字段","1. 在6份合同均已签署的情况下修改车牌号，观察重签提示和流程
2. 在6份合同均未签署的情况下修改车牌号，观察重签提示和流程
3. 在6份合同均已签署的情况下修改机动车所有人和所有人身份证明号码，观察重签范围
4. 在6份合同均未签署的情况下修改机动车所有人和所有人身份证明号码，观察重签范围
5. 在6份合同均已签署的情况下修改代理人，观察重签范围
6. 在6份合同均未签署的情况下修改代理人，观察重签范围","1. 系统提示需要重新签署6份合同，重签流程启动
2. 系统提示需要撤销合同并重新签署6份合同
3. 系统提示仅需重签《机动车所有人授权委托书》
4. 系统提示需要撤销合同，仅重签《机动车所有人授权委托书》
5. 系统提示需要重新签署《机动车所有人授权委托书》《抵押权人授权委托书》《机动车抵押登记/质押备案申请表》3份合同
6. 系统提示需要撤销合同，重新签署上述3份合同"
TC005,【修改车务订单】上传文件变更重签验证,P0,"至少有1个合同生成方式为上传文件在线签署","1. 修改抵押合同生成方式为上传文件在线签署的文件，观察重签提示
2. 修改机动车所有人授权委托书生成方式为上传文件在线签署的文件，观察重签提示
3. 修改抵押权人授权委托书生成方式为上传文件在线签署的文件，观察重签提示
4. 修改机动车抵押登记申请表生成方式为上传文件在线签署的文件，观察重签提示
5. 修改所有人声明书生成方式为上传文件在线签署的文件，观察重签提示
6. 修改抵押物清单生成方式为上传文件在线签署的文件，观察重签提示","1. 系统提示需要重新签署抵押合同
2. 系统提示需要重新签署机动车所有人授权委托书
3. 系统提示需要重新签署抵押权人授权委托书
4. 系统提示需要重新签署申请表
5. 系统提示需要重新签署所有人声明书
6. 系统提示需要重新签署抵押物清单"
TC006,【修改车务订单】提交按钮重签合同弹窗验证,P0,"存在触发重签合同条件的订单修改","1. 修改触发重签合同条件的字段后点击提交按钮，观察弹窗提示
2. 在弹窗中点击取消按钮，观察页面状态
3. 在弹窗中点击确定按钮，观察订单状态变化","1. 弹窗显示「提交后订单会重置，重新开启签署流程，您确定要修改吗？」
2. 弹窗关闭，修改内容未提交，页面保持修改状态
3. 弹窗关闭，订单重置，重新开启签署流程"
TC007,【修改车务订单】提交按钮重推交科所弹窗验证,P0,"存在触发重推交科所条件但不触发重签的订单修改","1. 在原工单状态为分单失败/待签署，新合同状态为签署完成的情况下，修改抵押权人委托书文件后点击提交按钮
2. 在工单状态不为分单失败或待签署，新合同状态为签署完成的情况下，修改相关文件后点击提交按钮
3. 在原工单状态为分单失败/待签署，新合同状态为待签署的情况下，修改车架号后点击提交按钮
4. 在工单状态不为分单失败或待签署，新合同状态为签署完成的情况下，修改车架号后点击提交按钮","1. 弹窗显示「提交后订单会重置，重走交科所推送流程，您确定要修改吗？」
2. 弹窗显示「提交后订单会重置，重走交科所推送流程，您确定要修改吗？」
3. 订单继续走合同签署流程，不显示重推弹窗
4. 弹窗显示「提交后订单会重置，重走交科所推送流程，您确定要修改吗？」"
TC008,【修改车务订单】提交按钮普通修改验证,P1,"存在不触发重签和重推条件的订单修改","1. 修改办理要求字段后点击提交按钮，观察系统处理
2. 修改车辆说明字段后点击提交按钮，观察系统处理
3. 验证修改后的订单状态和业务流程","1. 修改内容正常提交，无弹窗提示
2. 修改内容正常提交，无弹窗提示
3. 只变更数据，订单状态和业务流程都不变"
TC009,【修改车务订单】重新推送交科所固定字段验证,P0,"存在包含固定字段的车务订单","1. 修改号牌种类、车牌号、车架号等车辆信息，观察是否触发重推交科所
2. 修改抵押权人、抵押权人统一社会信用代码等抵押权人信息，观察是否触发重推交科所
3. 修改机动车所有人、所有人身份证明名称、所有人身份证明号码等所有人信息，观察是否触发重推交科所
4. 修改代理人、抵押合同编号、办理渠道等其他固定字段，观察是否触发重推交科所
5. 修改所有人身份证正反面原件、所有人现场照等文件，观察是否触发重推交科所","1. 修改车辆信息触发重新推送交科所流程
2. 修改抵押权人信息触发重新推送交科所流程
3. 修改所有人信息触发重新推送交科所流程
4. 修改其他固定字段触发重新推送交科所流程
5. 修改相关文件触发重新推送交科所流程"
TC010,【修改车务订单】上传已签署文件变更重推验证,P0,"6个合同生成方式中至少有1个为上传已签署文件","1. 在抵押合同生成方式为上传已签署文件的情况下，修改抵押合同文件，观察是否触发重推交科所
2. 在机动车所有人授权委托书生成方式为上传已签署文件的情况下，修改委托书文件，观察是否触发重推交科所
3. 在抵押权人授权委托书生成方式为上传已签署文件的情况下，修改委托书文件，观察是否触发重推交科所
4. 在申请表生成方式为上传已签署文件的情况下，修改申请表文件，观察是否触发重推交科所
5. 在所有人声明书生成方式为上传已签署文件的情况下，修改声明书文件，观察是否触发重推交科所
6. 在抵押物清单生成方式为上传已签署文件的情况下，修改清单文件，观察是否触发重推交科所","1. 修改抵押合同文件触发重新推送交科所流程
2. 修改机动车所有人授权委托书文件触发重新推送交科所流程
3. 修改抵押权人授权委托书文件触发重新推送交科所流程
4. 修改申请表文件触发重新推送交科所流程
5. 修改所有人声明书文件触发重新推送交科所流程
6. 修改抵押物清单文件触发重新推送交科所流程"
TC011,【修改车务订单】特定字段组合重签验证,P1,"存在设置了特定字段的模版合同","1. 修改《机动车所有人授权委托书》《抵押权人授权委托书》中设置的办理城市，观察重签范围
2. 修改《机动车所有人授权委托书》《抵押权人授权委托书》中设置的车架号，观察重签范围
3. 修改《抵押权人授权委托书》《机动车抵押登记/质押备案申请表》中设置的抵押权人，观察重签范围","1. 系统提示需要重新签署《机动车所有人授权委托书》《抵押权人授权委托书》2份合同
2. 系统提示需要重新签署《机动车所有人授权委托书》《抵押权人授权委托书》2份合同
3. 系统提示需要重新签署《抵押权人授权委托书》《机动车抵押登记/质押备案申请表》2份合同"
TC012,【修改车务订单】文件格式验证,P2,"合同生成方式为上传已签署文件或上传文件在线签署","1. 尝试上传非pdf格式的抵押合同文件，观察系统提示
2. 尝试上传非pdf格式的申请表文件，观察系统提示
3. 尝试上传非pdf格式的委托书文件，观察系统提示
4. 上传正确pdf格式的各类合同文件，观察上传结果","1. 系统提示文件格式不正确，要求上传pdf文件
2. 系统提示文件格式不正确，要求上传pdf文件
3. 系统提示文件格式不正确，要求上传pdf文件
4. 所有pdf格式文件正常上传成功"
TC013,【修改车务订单】边界值文件大小验证,P2,"合同生成方式为上传已签署文件或上传文件在线签署","1. 上传699KB的抵押合同文件，观察上传结果
2. 上传701KB的抵押合同文件，观察系统提示
3. 上传599KB的申请表文件，观察上传结果
4. 上传601KB的申请表文件，观察系统提示
5. 上传499KB的委托书文件，观察上传结果
6. 上传501KB的委托书文件，观察系统提示","1. 抵押合同文件上传成功
2. 提示「请上传700KB以下的文件」，上传失败
3. 申请表文件上传成功
4. 提示「请上传600KB以下的文件」，上传失败
5. 委托书文件上传成功
6. 提示「请上传500KB以下的文件」，上传失败"
TC014,【修改车务订单】权限验证,P1,,"1. 使用有修改权限的用户账号进入订单修改页面，观察页面功能
2. 使用无修改权限的用户账号尝试进入订单修改页面，观察系统响应
3. 使用有部分修改权限的用户账号进入订单修改页面，观察可操作字段","1. 有权限用户可以正常进入修改页面，所有功能正常使用
2. 无权限用户无法进入修改页面或显示权限不足提示
3. 部分权限用户只能修改有权限的字段，其他字段为只读状态"
TC015,【修改车务订单】数据一致性验证,P2,"存在已修改的车务订单","1. 修改订单信息后保存，刷新页面查看数据一致性
2. 同时打开同一订单的多个修改页面，在其中一个页面修改并保存，观察其他页面的数据更新
3. 修改订单后查看订单详情页面，验证数据同步情况","1. 页面刷新后显示最新的修改数据，数据保持一致
2. 其他页面显示数据更新提示或自动刷新显示最新数据
3. 订单详情页面显示最新的修改数据，与修改页面保持一致"
TC016,【修改车务订单】异常情况处理验证,P2,,"1. 在网络不稳定环境下进行订单修改操作，观察系统处理
2. 在文件上传过程中模拟网络中断，观察系统恢复机制
3. 在订单修改过程中模拟系统异常，观察错误处理机制","1. 网络不稳定时系统显示适当的错误提示和重试机制
2. 网络中断后系统提供文件上传恢复机制或重新上传提示
3. 系统异常时显示明确的错误信息和处理建议"
