---
type: "manual"
---

# 测试用例设计规则

## 1. 测试用例基本结构

### 1.1 字段定义

| 字段名   | 格式要求                                        | 必填 |
| --------| -----------------------------------------------| ---- |
| 用例编号 | TC001、TC002、TC003格式                         | 是    |
| 用例标题 | 【模块名称】+ 动宾短语                            | 是    |
| 重要程度 | P0/P1/P2                                       | 是    |
| 前置条件 | 执行用例前需要满足的条件                           | 否    |
| 测试步骤 | 每个步骤带序号，使用Excel可识别的换行符              | 是    |
| 预期结果 | 与测试步骤序号一一对应，使用Excel可识别的换行符       | 是    |

### 1.2 测试用例合并与拆分原则

⚠️ **合理合并，避免过度拆分**

#### 合并原则（推荐）

- **同页面同功能合并**：同一页面/功能内的相同测试点应合并为一条用例
- **测试点类型合并**：

  - 列表筛选条件验证（多个筛选条件合并）
  - 字段验证（多个字段边界值、输入限制合并）
  - 表单验证（多个字段验证合并）
  - 权限验证（多个权限场景合并）
  - 功能验证（同一个按钮多个操作场景合并）

- **步骤数量控制**：合并后的用例控制在8-12步内，确保可执行性

#### 拆分原则（仅在必要时）

- **跨功能模块**：不同功能模块的测试应拆分
- **不同页面**：不同页面的测试应拆分
- **复杂流程**：步骤超过12步的复杂流程可考虑拆分

#### 合并示例

````
✅ 推荐：列表筛选条件合并验证
TC001: 【列表功能】筛选条件综合验证
1. 在文本搜索框输入"测试"，点击查询按钮，观察列表数据变化
2. 点击重置按钮，观察列表数据恢复

TC002: 【新增功能】表单字段输入限制验证
1. 在[姓名]文本框中输入中文，检查页面交互
2. 在[姓名]文本框中输入数字，检查页面交互
3. 在[姓名]文本框中输入100位字符，检查页面交互
4. 在[姓名]文本框中输入101位字符，检查页面交互
2. 点击重置按钮，观察列表数据恢复

❌ 避免：过度拆分
TC001: 合同编号搜索验证（2步）
TC002: 合同名称搜索验证（2步）
TC003: 甲方公司搜索验证（2步）
````

### 1.3 精简原则

#### 核心要求

- **操作验证合并**：操作和验证应合并为一个步骤，避免将"点击按钮"和"查看结果"分成两个步骤
- **步骤精简**：每个步骤应包含完整的操作和对应的验证点
- **避免冗余**：不要将简单的操作拆分成多个步骤
- **一一对应**：测试步骤和预期结果必须一一对应，步骤数量必须等于预期结果数量

#### 正确示例

````
✅ 正确：操作和验证合并
步骤：点击保存按钮，查看保存结果
预期：保存成功，无错误提示

✅ 正确：多操作合并
步骤：在文本搜索框输入"测试"，点击查询按钮，观察查询结果
预期：搜索框显示输入内容，列表显示匹配的合同记录
````

#### 错误示例

````
❌ 错误：操作和验证分离
步骤1：点击保存按钮
步骤2：查看保存结果
预期1：点击保存按钮
预期2：保存成功，无错误提示

❌ 错误：简单操作拆分
步骤1：在搜索框输入内容
步骤2：点击查询按钮
步骤3：观察查询结果

❌ 错误：步骤与预期结果数量不匹配
步骤： 1. 点击保存按钮
      2. 完成审批流程
预期： 1. 保存成功
      2. 审批完成
      3. 合同状态更新
````

### 1.4 字段详细要求

#### 用例标题

- **固定句式**：【模块名称】+ 动宾短语，例如【合同管理】列表页字段显示验证；【合同管理】新建合同[保存]功能验证
- 模块名称严格按照需求文档中给定的内容，不可随意修改

#### 重要程度

- **P0**：核心业务流程（冒烟测试级别）
- **P1**：主要功能验证
- **P2**：边缘场景/异常流程

#### 前置条件
- 如无需特殊说明，该字段可为空
- **单个条件**：直接填写，无需引号
- **多个条件**：用双引号包围，条件间用分号(;)分隔

#### 测试步骤

- **参考句式**：在xx中输入xx，点击xx，检查xx等
- **精简原则**：操作和验证应合并为一个步骤，避免将"点击按钮"和"查看结果"分成两个步骤
- 每个步骤带序号，使用Excel可识别的换行符
- 系统文字描述使用[ ]标记，不要使用双引号标记（如页面名称、按钮名称、提示信息）
- 不能包含预期结果的验证
- 不允许出现模糊词汇（如可能、是否等）
- 操作步骤可独立执行
- 涉及具体数据时必须明确指定具体值

#### 预期结果

- **一一对应要求**：预期结果必须与测试步骤一一对应
- 期望的系统响应和状态
- 包含可验证的界面元素及数据变化
- 使用"显示"、"返回"、"跳转"等确定性动词
- 只能描述预期结果，不能包含步骤描述
- 涉及具体数据时必须明确验证具体顺序或数量
- **多预期结果格式**：当一个步骤有多个预期结果时，使用以下格式：

  ````
  9. 
  1、第一个预期结果
  2、第二个预期结果
  ````

## 2. 测试设计方法

### 2.1 基础方法（根据需求场景选择）

1. **等价类划分**：将输入数据划分为有效和无效等价类
2. **边界值分析**：测试边界条件（最小值、最大值、空值、临界长度等）
3. **判定表驱动法**：适用于多条件组合场景
4. **场景法**：基于用户实际使用场景设计用例
5. **错误猜测法**：基于经验推测可能的缺陷
6. **状态迁移法**：适用于具有明确状态转换的系统

### 2.2 异常场景测试（根据需求场景选择）

- **防抖机制验证**：连续快速点击验证
- **API响应时间验证**：超过1秒显示加载状态
- **网络异常处理**：网络中断/延迟/超时处理
- **并发操作处理**：多用户同时操作验证
- **数据异常处理**：空数据、异常格式、特殊字符处理
- **权限边界验证**：未登录用户、权限越界验证

## 3. CSV文件格式规范

### 3.1 基本要求

- **编码**：UTF-8编码，确保中文字符正确显示
- **分隔符**：严格使用英文逗号","，严禁使用中文逗号"，"
- **换行**：包含换行的内容必须用双引号包围，换行符使用Excel可识别的格式
- **目录位置**：文件存放在`/testcases/项目名称/`目录下
- **命名规范**：[功能模块]测试用例.csv

### 3.2 格式检查清单

生成CSV文件后必须检查：

- 所有字段分隔符都是英文逗号
- 用例标题中的模块名称与需求文档中一致
- 多个前置条件用分号分隔并用双引号包围
- 包含换行的内容用双引号包围
- 无需换行的内容，使用「」代替双引号
- 文件能在Excel中正确打开
- 所有字段都在正确的列中

## 4. 生成要求与流程

### 4.1 必须要求

1. **直接生成**：使用edit_file工具创建CSV格式测试用例文件
2. **数据完整性**：CSV文件包含所有必要的列和数据
3. **可执行性**：测试步骤具体明确，预期结果可验证
4. **覆盖率优先**：确保功能验证的完整性

### 4.2 工作流程

1. **需求分析**：理解功能结构、业务规则、约束条件
2. **设计用例**：结合测试设计方法，遵循合并与拆分原则
3. **创建CSV**：使用edit_file工具创建文件，确保格式规范
4. **验证结果**：检查CSV格式和覆盖率

### 4.3 质量标准

- **独立性**：每个测试用例独立执行
- **可重复性**：能够重复执行并得到一致结果
- **可验证性**：预期结果具体且可验证
- **清晰性**：描述清晰，无歧义
- **完整性**：包含执行所需的所有信息