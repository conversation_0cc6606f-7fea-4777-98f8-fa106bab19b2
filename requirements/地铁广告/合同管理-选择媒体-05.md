## 8. 汇总、明细列表
### 8.1 查询功能
输入框中输入后对媒体类型、投放档期、所属线别、所属站点、等级对明细表进行查询

### 8.2 汇总视图
默认展示汇总信息点击可切换汇总和明细查看
#### 8.2.1 展示字段
- 媒体类型
- 投放档期
- 数量
- 刊例合计（元）：所有媒体等级的刊例单价之和
#### 8.2.2 操作功能
**单条删除**：
- 点击操作列单条删除
- 二次确认弹窗
- 点击可删除整个媒体类型下的媒体
- 删除成功后切换到明细tab，数据更新
**批量删除**：
- 不选择数据点击批量删除：删除失败给出提示
- 选择一条数据点击批量删除，成功
- 选择2条以上数据点击批量删除，成功
- 选择所有数据点击批量删除，成功
- 删除成功后切换到明细tab，数据更新
#### 8.2.3 数据展示规则
有两个投放档期，选择的媒体类型汇总后有2条
- 投放档期1-媒体类型1
- 投放档期2-媒体类型1
- 投放档期1-媒体类型2
- 投放档期2-媒体类型2

### 8.3 明细视图
#### 8.3.1 展示字段
- 编号
- 媒体类型
- 投放档期
- 数量
- 刊例单价：价格管理中当前媒体对应当前等级设置的发布费价格、适用站点、适用线别
- 所属线别
- 所属站点
- 等级
- 所在位置
- 出口
#### 8.3.2 操作功能
**替换**：
- 展示替换弹窗
- **查询条件**
  - 编号：模糊查询、精确查询
  - 媒体/组合媒体：默认选择媒体，可切换选择组合媒体
  - 所属线别
  - 所属站点
  - 媒体类型
  - 等级
  - 所在位置
- **列表展示数据**
  - 展示登录人（乙方）所在公司空闲的媒体资产包内包含的媒体信息
  - 默认展示媒体可切换选择组合媒体
  - 客户报备、媒体库存
- **切换查询组合媒体时**
  - 展示登录人所在公司空闲的媒体资产包内包含的媒体信息
  - 已在当前列表的不展示
  - 意向中、已被锁位、已售出、不可用的不展示
  - 组合媒体里包含的组合销售可单独售卖媒体-在所选档期内可售卖，不可售卖时不展示整个组合媒体
- **点击替换选择当前媒体**
**删除**：
- 二次确认弹窗
- 点击可删除当前媒体
- 删除后切换到汇总tab-数据更新
#### 8.3.3 数据展示规则
有两个投放档期，选择的媒体2条
- 投放档期1-媒体1
- 投放档期2-媒体1
- 投放档期1-媒体2
- 投放档期2-媒体2

### 8.4 费用汇总
切换明细时不变，始终展示
#### 8.4.1 折前发布费总计
明细表中（每一行刊例单价*数量）*行数
#### 8.4.2 以量制价折后价
**触发了以量制价**：
- 每个媒体类型对应等级设置的刊例单价到达了以量制价的区间值
- 该媒体类型对应等级设置的刊例单价*以量制价折扣*数量*购买时长
**未触发以量制价**：
- 该媒体类型对应的等级设置的刊例单价*数量*购买时长

## 9. 基于以上选择自动生成广告位方案
### 9.1 广告位等级设置
- 广告位等级：S、A、A++
- 数量
  - 展示登录用户所在公司每个等级广告位的可用数量
  - 输入框内可输入0和正整数
  - 限制不能超过实际库存

### 9.2 功能按钮
- **重置按钮**：重置已输入的数字
- **自动生成广告位方案按钮**：展示选择自动方案弹窗

### 9.3 自动方案弹窗
#### 9.3.1 提示信息
根据您设置的需求，系统自动为您生成以下可选方案，选择时间倒计时5分钟
- 倒计时结束后所有一键选择按钮置灰不可点击
#### 9.3.2 期望广告位等级
- 显示媒体选择页面选择的输入的等级
- 输入0的等级不展示
#### 9.3.3 自动生成的方案
**方案1：客户偏好**
- 客户公司以前投放过广告：根据历史投放占比从高到低显示媒体类型级站点
- 客户公司以前没有投放过广告：随机生成
**方案2：行业优先**
- 根据客户品牌所属行业历史投放的媒体类型、等级及站点从高到低显示
**方案3：客流优先**
- 随机生成
#### 9.3.4 操作功能
- **点击站点名称**：展示选择站点弹窗（只能查看不可操作）
- **点击一键选择**：展示到明细及汇总表中

## 10. 页面操作
### 10.1 取消
提示取消后所更改信息不会带入，确认取消吗
- **取消**
  - 弹窗关闭，停在当前页面，数据不发生变化
- **确认**
  - 回到新增合同-投放信息页面，页面数据不发生
