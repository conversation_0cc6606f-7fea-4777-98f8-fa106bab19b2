# 合同管理-合同复核

## 1. 页面展示

### 1.1 数据显示

- 显示前两步填写的数据
- 数据不可编辑

## 2. 适用规则判断

### 2.1 执行率计算

**计算公式**：

````
执行率 = sum（执行中+已完成状态合同中广告位明细的投放数量）/ sum（已失效/审批中/已撤回/已驳回/已签约/执行中/已完成/作废中/已作废/终止中/已终止状态合同中广告位明细中的投放数量）
````

### 2.2 规则分支

#### 2.2.1 执行率>=80%

- 按照规则3执行
- 页面不展示规则选择

#### 2.2.2 执行率<80%

##### 当前公司类型=商业公司/广告公司

**按照规则1执行**：

- 提示：第一次预定/提交审批~审批通过共x天，您需在x天内完成合同审批
- X天的意思是营销规则中-合同广告位保护机制中该公司对应分类的审批完成天数

##### 当前公司类型≠商业公司/广告公司

**规则1和2都展示，可任选其一**：  
**选择规则2时**：

- 提示：第一次预订/提交审批~带媒体经营负责人审批共X天

  - X天的意思是营销规则中-合同广告位保护机制中该公司对应分类的审批完成天数

- 需预交支付服务金额****元

  - 金额 = 投放汇总-服务费合计

- **付款凭证、截图**：

  - 必填字段
  - 格式限制：图片
  - 数量限制：不超过5张
  - 功能：上传后可预览、可删除


## 3. 导出制式合同

### 3.1 显示条件

- 登录用户所在公司 = 商业公司、广告公司时展示导出按钮
- 否则隐藏按钮

### 3.2 导出功能

**点击下载word**：  
**文件名称规则**：

- 合同名称不为空：文件名称 = 合同名称
- 合同名称为空：文件名称 = 青岛地铁广告发布合同（品牌）  
  **文件内容**：
- 与模版一致
- **填充内容**：

  - 甲方乙方公司名称、公司地址、联系人信息
  - 发布媒体位置、形式、数量、期限


## 4. 页面操作

### 4.1 上一步操作

**点击上一步**：

- 弹出编辑未保存弹窗

  - **取消**：停在当前页面，数据不发生变化
  - **确定**：页面关闭，数据不保存


### 4.2 预订操作

#### 4.2.1 商业合同-广告位冲突

**当前合同=商业合同，且选择的广告位投放档期有不为空闲的**：

- **提示预订失败**：

  - 停留在当前页面
  - 提示信息：预定失败，点位【"+编号+"】档期（冲突的时间）"+别的账号xxx预定的与该投放档期冲突的投放档期+"已被别的账号锁位
  - **账号显示规则**：

    - 占用的公司=商业公司、广告公司：账号XXX显示用户名称（所属公司）
    - 否则：账号XXX显示所属公司
    - 有多个档期被不同的公司占用，则依次展示每一个占用的信息，用分号间隔

  - 页面上方显示提示信息


#### 4.2.2 商业合同-预订成功

**当前合同=商业合同，且选择的广告位投放档期都为空闲**：

- **提示预订成功**：

  - 回到合同管理列表页面
  - 合同状态 = 预定中

- **保护机制启动**：

  - 进行当前投放时间广告位的保护倒计时
  - 提示信息：此合同点位将保护x小时，超时将不予保护，请在倒计时结束前及时提交审批
  - X = 营销规则中-合同广告位档期保护机制-该公司所属类型设置的预定保护小时数/24
  - 小时数输入能被24整除

- **超时处理**：

  - 超过X天未提交审批，当前合同内所有的广告位及档期释放，其他合同可以选择


#### 4.2.3 储值合同

**当前合同=储值合同，且选择的广告位都空闲**：

- **余额>=0**：可保存并预订成功，锁定规则与商业合同一致
- **余额<0**：保存失败，提示预定失败，余额不可小于0

### 4.3 提交审批操作

#### 4.3.1 未点击过预订

- 锁位规则与预定的规则一致
- 保护时间倒计时 = 合同广告位档期保护机制-该公司所属类型设置的预定保护小时数/24
- **审批流程启动**：

  - 审批人收到审批消息通知
  - 合同状态 = 审批中
  - 回到合同管理列表页面


#### 4.3.2 点击过预订

- 保护倒计时 = （审批保护小时数-已经预订执行的保护小时数）/24
- 其他预订规则与未点击过预订直接点击审批一致

## 5. 审批流程处理

### 5.1 规则2、规则3审批

#### 5.1.1 审批通过

- 合同状态 = 已签约
- 合同内所有投放档期内的广告位明细不可用

#### 5.1.2 审批拒绝

**第一次拒绝**：

- 当前倒计时<2天：按照当前审批倒计时继续计时
- 当前倒计时>=2天：按照审批不通过的时间重新倒计时2天  
  **第三次拒绝**：
- 合同状态 = 已失效
- 广告位及档期全部释放
- 若是规则3，服务费不退  
  **审批时间规则**：
- 采用工作流的时间

#### 5.1.3 审批超时处理

- 场景：审批未到通过状态时，倒计时结束，有其他合同提交了重叠档期的广告位
- 验证点：

  - 审核通过其中一个合同，另一个合同自动审核拒绝
  - 另一个合同已审核不通过三次时，合同状态=失效
  -  另一个合同审核不通过不超过三次时，合同状态和保护机制按照提交和审批来判断


### 5.2 规则1审批

#### 5.2.1 审批通过

- 合同状态 = 已签约
- 合同内所有投放档期内的广告位明细不可用

#### 5.2.2 审批拒绝

**第一次拒绝**：

- 倒计时规则按照当前时间继续计时，不做修改  
  **第三次拒绝**：
- 合同状态 = 已失效
- 广告位及档期全部释放

#### 5.2.3 审批超时处理

场景：审批未到通过状态时，倒计时结束，有其他合同提交了重叠档期的广告位

验证点：

- 审核通过其中一个合同，另一个合同自动审核拒绝
- 另一个合同已审核不通过三次时，合同状态=失效
-  另一个合同审核不通过不超过三次时，合同状态和保护机制按照提交和审批来判断

