模块名称：电子围栏管理-货站
 页面路径验证
  基础数据管理>电子围栏管理>货站围栏
 页面数据权限验证
  用户授权页面，分配的数据权限
 页面刷新规则验证
  进入页面
   数据刷新
  手动点击 刷新
   页面数据刷新
 列表排序规则验证
  查看列表数据排序，根据创建时间倒序排列
 数据来源验证
  数据根据本页面手动维护
 筛选条件验证
  围栏类型
   下拉框，默认？，下拉值 ：基地、 短驳、中心、网点、SC围栏、提货点、货站、港口、客户

  站点名称
   文本框，模糊搜索
  站点编码
   文本框，模糊搜索
  围栏状态
   下拉框,无默认值，下拉值：启用、禁用

  审批状态
   下拉框，默认选择全部，下拉选项：直接通过 审批中 生效 未生效

 查询功能验证
  筛选条件 单个查询，验证列表展示数据是否正确，统计的计数是否正确
  筛选条件 组合查询，验证列表展示数据是否正确，统计的计数是否正确
 列表字段验证
  字段显示顺序：货站类别、货站名称、货站编码、审批状态（直接通过 审批中 生效 未生效）、发货方编码、送达方编码、地址、经度、纬度、省、市、区、行政区编码、围栏半径

  操作栏：编辑、启用/禁用、通过、 驳回、 审批记录

 新增字段验证
  货站类别
   必填项，下拉框，下拉选项：一次货站、二次货站

  货站名称
   必填项，文本框，手动输入文本，字符长度验证？

  货站编码
   必填项，一次货站为手动编码，二次货站为系统编码，编码生成规则：发货方机构编码+HZ+5位数字顺序编码

  发货方
   必填，一次货站输入园区名称，二次货站输入中心名称
  发货方编码
   必填，系统自动填充，所有的编码都由oms推送
  送达方
   必填，一次货站输入中心名称，二次货站输入网点/SC等名称
  送达方编码
   必填，系统自动填充，所有的编码都由oms推送
  地址
   必填，手动输入，可根据地图选点自动带入地址信息
  子主题
  经度
   必填项，地图接口 实时解析地图位置，支持手动修改，保留小数点后6位，调整完的经纬度与详细地址进行对比，距离需在1.5km之内，如超出范围则提示“地址与经纬度距离差距相差较大 请确认地址/重新解析”

  纬度
   必填项，地图接口 实时解析地图位置，支持手动修改，保留小数点后6位，调整完的经纬度与详细地址进行对比，距离需在1.5km之内，如超出范围则提示“地址与经纬度距离差距相差较大 请确认地址/重新解析”
  省市区
   必填，行政区划选择
  行政区划编码
   必填，根据选择的省市区 自动生成填充，切换省市区时 编码同步切换
  围栏半径
   必填项 默认1.5km，可手动编辑
  审批状态？

   必填，下拉选项？选项：直接通过、待审批、生效、未生效

  货站联系人
   非必填，下拉匹配Hworl中的员工数据
  货站电话
   非必填，文本框，11位校验，电话格式验证
  货站真伪
   必填项，下拉框，下拉选项：真实、虚拟，默认选择【真实】

  货站到中心距离
   非必填，文本框，手动输入文本，字符长度？

  月均方量
   非必填，文本框，手动输入文本，字符格式限制？字符长度？

  提报人
   非必填，下拉匹配hwork中员工信息
  围栏状态
   必填项，下拉框，下拉选项：启用、禁用，默认选择【启用】

  审批记录
   显示提交人、提交时间、审批人、审批时间、审批备注
 新增功能验证
  字段边界值/格式验证
  必填项验证
  非必填项验证
  字段唯一性验证，货站名称+发货方编码+送达方编码 三个字段合并校验

  存在一个基地下创建两条类似（相近）的围栏，再次创建时，提示【是否继续创建】

  新增时，货站围栏，运营维护时调用地图poi点，校验位置附近300m内是否是仓库、物流公司  如果有对应的信息，则直接通过，不需要走审批流

 编辑功能验证
  不可修改字段
  不修改数据  点击保存
  所有字段均可编辑
  修改地址时，重新校验地图poi点，提交时，走审批流
  编辑围栏状态，如果不在使用货站送达方是，可以选择禁用，禁用后外部系统就不再新调取围栏做校验
  修改数据 点击保存，查看列表信息是否更新，再次进入编辑页面 数据是否更新
  编辑地址时，修改了地址信息，地址信息与poi点能匹配，不走审批流

  编辑地址时，修改了地址信息，地址信息与poi点不能匹配，需要重新走审批流

 地图样式验证
  地图初始中心点
  列表显示的围栏，地图上是否全部正确展示
  地图搜索功能验证
  围栏样式显示是否正确
  围栏上方气泡显示围栏名称【xxxx围栏】
  地图选中围栏后，列表上方显示 【当前围栏 ：xxxxx  围栏半径 xxxxx  围栏经纬度 xxxx】，地图显示围栏半径

 批量导入功能验证
  必填项验证
  字段默认值验证
  字符长度验证
  下拉选项是否与平台一致
  能否成功上传数据
  上传的内容不正确时，是否有错误信息提示
  文件内有正确和错误的数据，能否成功上传正确的 并过滤掉错误的
 启用/禁用按钮验证
  根据新增时选择的信息显示相反的按钮，启用的围栏显示禁用按钮，禁用的围栏显示启用按钮
  点击启用，围栏启用，外部系统可调度围栏
  点击禁用，围栏禁用，外部系统不可调取围栏
 审批流按钮验证

  根据用户角色限制按钮是否显示
  配主管/线长角色的用户显示该按钮
  审批没有根据poi点匹配的围栏信息
  按钮包含  【通过】【驳回】【审批记录】