一次车次单轨迹查看
 页面路径验证
  运输视图>轨迹分析工作台
 页面数据权限验证
  用户授权页面，分配的数据权限
 页面刷新规则验证
  进入页面
   数据刷新
  手动点击 刷新
   页面数据刷新
 数据源验证
  车辆数据来自车辆表
  设备定位从数据库表读取
 筛选条件验证
  单据号
   下拉框，默认不选择 加载全部信息，下拉选项;提单号、运单号、订单号、车次号
  时间选择器
   默认选择今天？精确到天，根据日期查询列表数据

 单据卡片验证-一次整车、零担车次
  基地
   数据来源：lesmdm

  车队名称
   正常显示数据，数据与上游系统显示是否一致
  送达方
   正常显示数据，数据与上游系统显示是否一致
  送达模式
   正常显示数据，数据与上游系统显示是否一致数值参考：送达中心、直发客户、拼车

  订单状态
   正常显示数据，数据与上游系统显示是否一致，参考值：未指派、已指派、装货中、在途、已到达、已签收

  车牌号
   正常显示数据，数据与上游系统显示是否一致，如果有改派的情况 则显示【改派前车牌号A,改派后车牌号B】
  是否达标
   正常显示数据，系统计算，计算逻辑是？，参考值：未计算、未达标、达标

  总在线率
   正常显示数据，系统计算，未完成的单据-计算实时在线率，已签收的单据-计算总在线率。 单据状态从哪里来？

  轨迹状态
   正常展示数据，系统计算，参考值：有轨迹、无轨迹、未计算

 单据卡片验证-一次短驳车次
  基地
   数据来源：lesmdm

  车队名称
   正常显示数据，数据与上游系统显示是否一致
  起始地
   正常显示数据，数据与上游系统显示是否一致
  目的地
   正常显示数据，数据与上游系统显示是否一致
  订单状态
   正常显示数据，数据与上游系统显示是否一致，参考值：未指派、已指派、装货中、在途、已到达、已签收

  车牌号
   正常显示数据，数据与上游系统显示是否一致，如果有改派的情况 则显示【改派前车牌号A,改派后车牌号B】
 卡片操作栏功能验证-一次整车车次号
  围栏计算
   订单状态为【已指派】且【未到达】的车次
   点击按钮，出发围栏计算，计算进入基地时间，离开提货点时间，离开基地时间，进入中心时间
   计算数据是否准确
  围栏重算
   触发场景：客户修改了地址，送到方位置变化了，需要重新计算

   送达模式为【直发客户】且产生了离开基地时间/离开提货点时间

   点击按钮，系统重新计算目的地电子围栏
   如果没有触发条件，点击按钮，围栏不变
   重算的围栏是否准确
  更换绑定
   前置条件：未产生到达时间的车次，手动更换设备

   产生到达时间的车次，点击按钮无效
  解绑?这里没有这个功能了？

   订单状态已产生到达时间的车次
   点击按钮，手动解绑设备
  围栏时间
   未产生的订单节点时间
   手动维护：进入基地时间、离开提货点时间、离开基地时间、进入中心时间

   所有时间都是必填项？

   时间精确到秒？

   时间重复可以吗？

  在线率重算（刷新在线率）

   订单状态【已签收】，且没有计算周期限制
   点击按钮 重新计算在线率
  在线率修改
   订单状态【已签收】，且没有计算周期限制

   手动属该在线率数值，修改成什么都需要根据线下邮件，系统不做限制
 卡片操作栏功能验证-一“一次短驳车次号”

  围栏计算
   订单状态为【已指派】且【未到达】的车次
   点击按钮，出发围栏计算，计算进入基地时间，离开提货点时间，离开基地时间，进入中心时间
   计算数据是否准确
  围栏时间
   未产生的订单节点时间
   手动维护：进入基地时间、离开提货点时间、离开基地时间、进入中心时间

   所有时间都是必填项？

   时间精确到秒？

   时间重复可以吗？

 卡片操作栏功能验证-零单车次号

  围栏计算
   订单状态为【已指派】且【未到达】的车次
   点击按钮，出发围栏计算，计算进入基地时间，离开提货点时间，离开基地时间，进入中心时间
   计算数据是否准确
  更换绑定
   前置条件：未产生到达时间的车次，手动更换设备

   产生到达时间的车次，点击按钮无效
  解绑?这里没有这个功能了？

   订单状态已产生到达时间的车次
   点击按钮，手动解绑设备
  围栏时间
   未产生的订单节点时间
   手动维护：进入基地时间、离开提货点时间、离开基地时间、进入中心时间

   所有时间都是必填项？

   时间精确到秒？

   时间重复可以吗？

 配置项&气泡窗验证-送达方围栏

  默认勾选
  展示围栏icon
  缩放到一定层级展示围栏
  气泡展示字段验证：送达方名称、围栏半径、经纬度、位置

  验证围栏信息是否准确
 配置项&气泡窗验证-发货围栏

  默认勾选
  展示围栏icon
  缩放到一定层级展示围栏
  气泡展示字段验证：送达方名称、围栏半径、经纬度、位置

  验证围栏信息是否准确
 配置项&气泡窗验证-优化后轨迹

  默认勾选
  地图上显示不同颜色的轨迹线，用虚线链接
  包括对 漂移点过滤、轨迹纠偏、轨迹点补偿处理后的完整轨迹
  验证计算后的轨迹信息显示是否准确
 配置项&气泡窗验证-连线
  默认勾选
  不同颜色区分出上报的轨迹点信息
  上报来源验证： 车载、便携、中交、APP

 配置项&气泡窗验证-轨迹点
  默认不勾选
  根据不同来源区分上报的轨迹点信息
  气泡信息也用不同颜色展示
  气泡信息展示字段验证： 车牌号、设备编码、设备类型、定位时间、接收时间、速度、方向、经纬度、位置

  车辆设备信息验证是否与车辆管理一致
  速度是否精确到小数点后两位，数值是否正确
  方向角与地图显示是否一致
  经纬度是否精确到小数点后6位
  经纬度与位置显示是否一致
  定位时间和接收时间 是否符合逻辑

 配置项&气泡窗验证-停车点
  默认不勾选
  勾选后 地图显示停车点车辆icon
  气泡显示字段验证：车牌号、设备编码、设备类型、开始时间、结束时间、停留时长、停留位置、关联围栏

  车辆设备信息验证是否与车辆管理一致
  开始时间、结束时间 是否精确到秒，且结束时间晚于开始时间
  停留时长计算是否准确
  停留位置与围栏是否相对应
  显示围栏 名称是否正确与平台信息一致
 配置项&气泡窗验证-报警点
  默认不勾选
  勾选后 地图显示报警点车辆icon
  气泡显示字段验证：车牌号、设备编码、设备类型、报警类型、报警等级、开始时间、结束时间、报警时长、报警位置

  车辆设备信息验证是否与车辆管理一致
  开始时间、结束时间 是否精确到秒，且结束时间晚于开始时间
  报警时长计算是否准确
  报警等级与报警类型是否一致
 配置项&气泡窗验证-改派点
  默认勾选
  勾选后 地图显示改派点icon
  气泡显示字段信息：改派时间、改派位置、改派前车辆、改派后车辆、改派点设备编码、改派后设备编码、改派前设备类型、改派后设备类型

  改派信息从哪里获取？

  验证字段信息展示是否准确
 配置项&气泡窗验证-签收点
  默认勾不勾选？

  勾选后，地图显示签收点icon
  气泡显示字段验证：签收时间、签收位置、签收定位方式（车载设备/便携设备/中交/app）

  签收设备编码，APP展示空，其他展示设备编号

 页面底部tab页验证-轨迹列表
  查询条件：根据单据的开始日期，以1h为时间段做拆分，用户需选择某个时间段

  用户选择时间后，列表加载显示匹配的数据
  用户点击数据，地图匹配显示对应的气泡数据
  列表字段显示验证：设备编码/车辆ID、定位时间、接收时间、经度、纬度、位置、采集方式、速度（km/h）、方向、设备类型、设备厂商

  位置字段显示【查看】按钮，点击查看精确的位置信息
  验证字段信息回显是否正确
 页面底部tab页验证-行驶列表
  根据选择的单据号显示信息
  列表字段验证：开始时间、结束时间、行驶时长、行驶里程、开始位置、结束位置

 页面底部tab页验证-停留列表
  根据选择的单据号显示信息
  列表字段验证：开始时间、结束时间、停留时长、停留位置、关联围栏
